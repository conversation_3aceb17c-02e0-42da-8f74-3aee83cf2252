<template>
  <div class="con">
    <div class="top-bar-con">
      <div class="select">
        <div class="select-title">接收机:</div>
        <n-select
          v-model:value="store.station"
          placeholder="请选择接收机"
          @update:value="store.selectChangeHandle"
          :options="
            store.transmitterList.map((item) => ({
              label: item.label,
              value: item.value,
            }))
          "
        />
      </div>
      <div class="top-bar">
        <el-button-group>
          <el-button
            :color="themeStore.themeColor"
            :plain="btnIndex === 0 ? false : true"
            @click="() => onBtnGroupClick(0)"
            >原始数据</el-button
          >
          <el-button
            :color="themeStore.themeColor"
            :plain="btnIndex === 1 ? false : true"
            @click="() => onBtnGroupClick(1)"
            >优选数据</el-button
          >
        </el-button-group>
      </div>
    </div>

    <raw v-if="btnIndex === 0" />
    <preferred v-else-if="btnIndex === 1" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import preferred from "./preferred.vue";
import raw from "./raw.vue";
import { useTimeDiffData } from "@/store/modules/row-data/time-diff-data";
import { useThemeStore } from "@/store/modules/theme";
const themeStore = useThemeStore();
const store = useTimeDiffData();
const btnIndex = ref(0);

const onBtnGroupClick = (index) => {
  btnIndex.value = index;
};
</script>

<style lang="scss" scoped>
.con {
  .top-bar-con {
    position: relative;
    .select {
      width: 250px;
      position: absolute;
      left: 450px;
      top: 15px;
      display: flex;
      align-items: center;
      z-index: 9;
      .select-title {
        width: 80px;
        font-size: 17px;
      }
    }
  }

  .top-bar {
    position: absolute;
    top: 12px;
    right: 16px;
    z-index: 1;
  }
}
</style>
