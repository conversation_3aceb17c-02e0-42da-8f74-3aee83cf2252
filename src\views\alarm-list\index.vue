<template>
  <div class="con custom-template">
    <div class="top-bar">
      <el-form inline :model="fromData">
        <el-form-item
          label="起止时间"
          class="date-picker"
          v-if="store.initType == 'historyAlarmData'"
        >
          <n-date-picker
            v-model:value="fromData.date"
            type="datetimerange"
            separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :is-date-disabled="disabledDate"
          />
        </el-form-item>
        <el-form-item
          v-for="key in fromParams"
          :key="key.value"
          :label="key.title"
        >
          <n-select
            v-model:value="fromData[key.value]"
            filterable
            tag
            placeholder="请选择"
            class="form-item"
            :options="
              key.data.map((item) => ({ label: item.label, value: item.value }))
            "
          />
        </el-form-item>
      </el-form>

      <div
        style="
          display: flex;
          align-items: center;
          gap: 20px;
          margin-left: auto;
          margin-right: 20px;
        "
      >
        <el-button-group class="">
          <el-button
            :color="themeStore.themeColor"
            :plain="store.initType != 'historyAlarmData' ? false : true"
            @click="() => store.onChildTabClick('realTimeAlarmData')"
            >实时数据</el-button
          >
          <el-button
            :color="themeStore.themeColor"
            :plain="store.initType == 'historyAlarmData' ? false : true"
            @click="() => store.onChildTabClick('historyAlarmData')"
            >历史数据</el-button
          >
        </el-button-group>
        <el-button
          class=""
          type="primary"
          @click="onSearchBtnClick"
          :loading="store.isLoading"
          >查询</el-button
        >
        <div class="btn" style="width: 100px" @click="onPinIt">
          <el-icon><Tools /></el-icon><span>参数告警</span>
        </div>
      </div>
    </div>
    <div class="content" v-loading="store.isLoading">
      <historyData
        ref="historyDataRef"
        v-show="store.initType == 'historyAlarmData'"
      />
      <realTimeData
        ref="realTimeDataRef"
        v-if="store.initType != 'historyAlarmData'"
      />
    </div>
  </div>

  <el-dialog
    v-model="alarmData.dialogVisible"
    title="告警参数"
    width="750"
    class="dialogs"
    v-loading="loading"
  >
    <div class="dialogsNav">
      <n-select
        v-model:value="alarmData.transmitterId"
        placeholder="请选择"
        class="select"
        @update:value="(value) => getsetAlarmSettings()"
        :options="
          transmitterIdList.map((item) => ({
            label: item.transmitterName,
            value: item.transmitterId,
          }))
        "
      />
      <el-checkbox
        v-model="alarmDettings.isIgnoreMaster"
        label="一键全部屏蔽"
        size="large"
        class="pingbi select"
        :true-label="1"
        :false-label="0"
        @change="setIgnoreMaster"
      />
    </div>
    <el-tabs
      v-model="alarmData.activeName"
      class="demo-tabs"
      @tab-click="getsetAlarmSettings"
    >
      <el-tab-pane
        v-for="item in alarmData.alarmList"
        :key="item.id"
        :label="item.name"
        :name="item.apiType"
      >
        <div v-if="item.type == 'range'" class="alarmDataNeoBox">
          <div class="itemBox">
            <div class="itemTitle">一级警告</div>
            <el-input-number
              class="itemnumbers"
              v-model="alarmDettings.alarm1"
              controls-position="right"
              size="large"
              :disabled="loading"
            />
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">二级警告</div>
            <el-input-number
              class="itemnumbers"
              v-model="alarmDettings.alarm2"
              controls-position="right"
              size="large"
              :disabled="loading"
            />
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">三级警告</div>
            <el-input-number
              class="itemnumbers"
              v-model="alarmDettings.alarm3"
              controls-position="right"
              size="large"
              :disabled="loading"
            />
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">场强阻断值</div>
            <el-input-number
              class="itemnumbers"
              v-model="alarmDettings.fieldStrengthBlock"
              controls-position="right"
              size="large"
              :disabled="true"
            />
            <span class="unit">dB</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">屏蔽截止时间</div>
            <n-date-picker
              style="width: 200px"
              v-model:value="alarmDettings.ignoreTime"
              type="datetime"
              placeholder="选择日期时间"
              :is-date-disabled="disabledDateignoreTime"
              :disabled="loading"
            />
            <!-- <span class="unit">s</span> -->
          </div>
          <el-checkbox
            v-model="alarmDettings.isIgnore"
            label="选中屏蔽"
            size="large"
            class="pingbi"
            :true-label="1"
            :false-label="0"
            :disabled="loading"
          />
        </div>
        <div class="itemandeBox" v-if="item.type == 'negativePositive'">
          <div class="nameBox">
            <div class="nameItem">偏差</div>
          </div>
          <div class="setjxz">
            <!-- <div class="setjxz_name">自动基线值</div>
            <el-input-number
              class=""
              controls-position="right"
              size="large"
              v-model="alarmDettings.syslineValue"
              :disabled="true"
            /> -->

            <div class="setjxz_name">手动基线值</div>
            <!-- <el-checkbox
              v-model="alarmDettings.baselineValue"
              label="采用自动基线值"
              size="large"
              class="setjxz_name_checked"
              :true-value="1"
              :false-value="0"
              :disabled="loading"
            /> -->
            <el-input-number
              class=""
              controls-position="right"
              size="large"
              v-model="alarmDettings.baselineValue"
              :disabled="loading"
            />
          </div>
          <div class="itemBox">
            <div class="itemTitle">一级警告</div>
            <el-input-number
              class="itemnumbers itemnumbersTwo"
              controls-position="right"
              size="large"
              v-model="alarmDettings.alarm1"
              :disabled="loading"
            />
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">二级警告</div>
            <el-input-number
              class="itemnumbers itemnumbersTwo"
              controls-position="right"
              size="large"
              v-model="alarmDettings.alarm2"
              :disabled="loading"
            />
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">三级警告</div>
            <el-input-number
              class="itemnumbers itemnumbersTwo"
              controls-position="right"
              size="large"
              v-model="alarmDettings.alarm3"
              :disabled="loading"
            />
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="itemBox">
            <div class="itemTitle">屏蔽截止时间</div>
            <n-date-picker
              style="width: 200px"
              v-model:value="alarmDettings.ignoreTime"
              type="datetime"
              placeholder="选择日期时间"
              :is-date-disabled="disabledDateignoreTime"
              :disabled="loading"
            />
          </div>

          <el-checkbox
            v-model="alarmDettings.isIgnore"
            label="选中屏蔽"
            size="large"
            class="pingbi"
            :true-label="1"
            :false-label="0"
            :disabled="loading"
          />
        </div>
        <div v-if="item.type == 'other'" class="alarmDataNeoBox">
          <div
            class="itemBox"
            style="
              display: flex;
              align-items: center;
              gap: 20px;
              margin-top: 10px;
            "
            v-for="i in item.listData"
            :key="i.key"
          >
            <div>
              <div class="itemTitle">{{ i.name }}的屏蔽截止时间</div>
              <n-date-picker
                v-model:value="i.ignoreTime"
                type="datetime"
                placeholder="选择日期时间"
                :is-date-disabled="disabledDateignoreTime"
                :disabled="loading"
              />
            </div>
            <el-checkbox
              style="padding-top: 30px"
              v-model="i.isIgnore"
              label="屏蔽"
              size="large"
              :true-label="1"
              :false-label="0"
              :disabled="loading"
              :key="`checkbox-${i.key}-${i.isIgnore}`"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="setAlarmSettings" type="primary">设置</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import emptyIcon from "@/assets/imgs/no_data.png";
import { useAlarmInfo } from "@/store/modules/alarm-list";
import {
  onMounted,
  watch,
  ref,
  reactive,
  getCurrentInstance,
  nextTick,
  onUnmounted,
} from "vue";
import { useThemeStore } from "@/store/modules/theme";
import historyData from "./historyData.vue";
import realTimeData from "./realTimeData.vue";
import apiAjax from "@/api";
import { ElMessage, ElMessageBox } from "element-plus";
import { unsubscribeAll } from "@/api/ws";

const { proxy } = getCurrentInstance();
const historyDataRef = ref();
const realTimeDataRef = ref();
let newlodalarmDettings = {
  alarm1: 0,
  alarm2: 0,
  alarm3: 0,
  alarmField: "",
  alarmFrequency: 0,
  baselineValue: 0,
  fieldStrengthBlock: 0,
  ignoreTime: null,
  isIgnoreMaster: 0, // 这个是是否全部屏蔽的
  isIgnore: 0, //0：不忽略，1：忽略
  isSysline: 0, //0：不采用，1：采用
  syslineValue: 0,
  timeSeconds: 0,
  timestamp: "",
  transmitter: "",
};
const loading = ref(false);

// 发波台
const transmitterIdList = Object.keys(proxy.configure.typeObj).map((key) => {
  return {
    transmitterId: key,
    transmitterName: proxy.configure.typeObj[key],
  };
});

const themeStore = useThemeStore();
const store = useAlarmInfo();

const fromData = ref(store[store.initType].getListApiParams);
const fromParams = ref(store[store.initType].fromParams);
watch(
  () => store.initType,
  () => {
    fromData.value = store[store.initType].getListApiParams;
    fromParams.value = store[store.initType].fromParams;
    if (store.initType !== "realTimeAlarmData") {
      store.search(store.initType);
    }
  },
);

const onSearchBtnClick = () => {
  store.search(store.initType);
};

const onDoBtnClick = () => {};

const onMarkBtnClick = () => {
  store.isMaskedState = true;
};

const onBackBtnClick = () => {
  store.isMaskedState = false;
};
const disabledDate = (time) => {
  const currentDate = new Date();
  // Handle both timestamp (from Naive UI) and Date object (from Element Plus)
  const timeValue = typeof time === "number" ? time : time.getTime();
  return timeValue > currentDate.getTime();
};
const disabledDateignoreTime = (time) => {
  const currentDate = new Date();
  // Handle both timestamp (from Naive UI) and Date object (from Element Plus)
  const timeValue = typeof time === "number" ? time : time.getTime();
  return timeValue < currentDate.getTime();
};

// 设置报警参数
const setAlarmSettings = async () => {
  const currentTab = alarmData.activeName;
  const now = new Date();
  const timeSeconds = Math.floor(now.getTime() / 1000);
  let list = [];
  if (currentTab == "Other") {
    list = alarmData.alarmList[8].listData.map((i) => {
      return {
        ...newlodalarmDettings,
        isSysline: 0,
        timeSeconds: timeSeconds,
        alarmField: alarmData.transmitterId + "-" + i.key,
        ignoreTime:
          i.ignoreTime == 0 || i.ignoreTime == null
            ? null
            : Math.floor(i.ignoreTime / 1000),
        isIgnore: i.isIgnore,
      };
    });
  } else {
    list = [
      {
        ...alarmDettings.value,
        isIgnore: alarmDettings.value.isIgnore ? 1 : 0,
        isSysline: 0,
        alarmField: alarmData.transmitterId + "-" + alarmData.activeName,
        transmitter: alarmData.transmitterId,
        timeSeconds: timeSeconds,
        ignoreTime:
          alarmDettings.value.ignoreTime == 0 ||
          alarmDettings.value.ignoreTime == null
            ? null
            : Math.floor(alarmDettings.value.ignoreTime / 1000),
      },
    ];
  }
  const data1 = await apiAjax.post("/api/jnx/dataAlarm/alarmSettings", [
    ...list,
  ]);
  if (data1) {
    ElMessage({
      type: "error",
      message: "设置失败",
    });
  } else {
    ElMessage({
      type: "success",
      message: "设置成功",
    });
  }
  await apiAjax.post(
    "/api/jnx/dataAlarm/settingShield?isIgnoreMaster=" +
      alarmDettings.value.isIgnoreMaster,
    {},
  );
  alarmData.dialogVisible = false;
};

const setIgnoreMaster = (value) => {
  console.log("setIgnoreMaster:", value);
  // 这里可以添加一键全部屏蔽的逻辑
};

const onPinIt = async () => {
  try {
    alarmData.dialogVisible = true;
    await getsetAlarmSettings();
  } catch (error) {
    console.error("Error opening alarm settings dialog:", error);
    alarmData.dialogVisible = false;
  }
};

const getsetAlarmSettings = async (tab) => {
  try {
    // 设置loading状态为true
    loading.value = true;

    // 如果有tab参数，使用tab.props.name作为当前激活的标签页
    const currentTab = tab ? tab.props.name : alarmData.activeName;
    console.log("alarmData", currentTab);
    const res = await apiAjax.post(
      `/api/jnx/dataAlarm/alarmSettingsSerach?alarmField=${currentTab}&transmitter=${alarmData.transmitterId}`,
      {},
    );
    alarmDettings.value = { ...newlodalarmDettings };
    alarmData.alarmList[8].listData.forEach((i) => {
      i.ignoreTime = null;
      i.isIgnore = 0;
    });
    if (res && res[0]) {
      if (currentTab == "Other") {
        const listData = alarmData.alarmList[8].listData;
        res.forEach((i) => {
          let item = listData.find((j) => j.key == i.alarmField.split("-")[1]);
          if (item) {
            // 使用 Vue 的响应式更新方式
            Object.assign(item, {
              ignoreTime: i.ignoreTime == 0 ? null : i.ignoreTime * 1000,
              isIgnore: Number(i.isIgnore),
            });
          }
        });
      } else {
        alarmDettings.value = {
          ...res[0],
          ignoreTime: res[0].ignoreTime == 0 ? null : res[0].ignoreTime * 1000,
          isIgnore: Number(res[0].isIgnore),
          isIgnoreMaster: Number(res[0].isIgnoreMaster),
        };
      }
    }
  } catch (error) {
    console.error("Error in getsetAlarmSettings:", error);
  } finally {
    // 查询完成后设置loading状态为false
    loading.value = false;
  }
};

onMounted(async () => {
  await store.init();
});

const alarmData = reactive({
  transmitterId: "6000M",
  infos: false,
  value1: "",
  value4: "",
  dialogVisible: false,
  activeName: "Block",
  alarmList: [
    {
      id: 1,
      name: "阻断时间",
      unit: "分钟",
      apiType: "Block",
      type: "range",
    },
    {
      id: 2,
      name: "授时偏差",
      unit: "ns",
      type: "negativePositive",
      info: false,
      apiType: "Tser",
    },
    {
      id: 3,
      name: "TOC",
      unit: "ns",
      type: "negativePositive",
      info: false,
      apiType: "Toc",
    },
    {
      id: 4,
      name: "场强",
      unit: "dBμV/m",
      type: "negativePositive",
      info: false,
      apiType: "FieldStrength",
    },
    {
      id: 5,
      name: "包周差",
      unit: "μs",
      type: "negativePositive",
      info: false,
      apiType: "PackageTD",
    },
    {
      id: 6,
      name: "SNR",
      unit: "dB",
      type: "negativePositive",
      info: false,
      apiType: "Snr",
    },
    {
      id: 7,
      name: "TOA",
      unit: "ns",
      type: "negativePositive",
      info: false,
      apiType: "Toa",
    },
    {
      id: 8,
      name: "频率偏差",
      unit: "Hz",
      type: "negativePositive",
      info: false,
      apiType: "FrequencyTD",
    },
    {
      id: 9,
      name: "其他屏蔽时长设置",
      unit: "",
      type: "other",
      info: false,
      apiType: "Other",
      listData: [
        {
          key: "Sys",
          id: 1,
          name: "系统告警",
          ignoreTime: null,
          isIgnore: 0,
        },
        {
          key: "TimeMark",
          id: 1,
          name: "时码比对",
          ignoreTime: null,
          isIgnore: 0,
        },
        {
          key: "Cfz",
          id: 1,
          name: "差分修正异常",
          ignoreTime: null,
          isIgnore: 0,
        },
        {
          key: "Dut1",
          id: 1,
          name: "DUT1异常",
          ignoreTime: null,
          isIgnore: 0,
        },
        {
          key: "Dwd",
          id: 1,
          name: "授时电文",
          ignoreTime: null,
          isIgnore: 0,
        },
      ],
    },
  ],
});

//设置传输的或者显示的值
const setFromData = (type, data) => {};

// 这个是值 // 也是传输的值
let alarmDettings = ref({ ...newlodalarmDettings });
const objColor = {
  typesOf: {
    1: {
      name: "一级警报",
      color: "serious",
    },
    2: {
      name: "二级警报",
      color: "same",
    },
    3: {
      name: "三级警报",
      color: "normal",
    },
  },
  state: {
    1: {
      name: "已处理",
      color: "normal",
    },
    2: {
      name: "未处理",
      color: "serious",
    },
  },
};
onUnmounted(() => {
  unsubscribeAll();
});
</script>

<style lang="scss" scoped>
.con {
  .top-bar {
    display: flex;
    background-color: #fff;
    align-items: center;
    height: 56px;
    .form-item {
      width: 180px;
    }
    :deep(.el-form-item) {
      margin: 12px;
    }
  }

  .content {
    .alarm-type {
      display: flex;
      align-items: center;

      .point {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin: 0;
        margin-right: 4px;
      }

      .text {
        display: inline-block;
      }
    }

    .op-con {
      display: flex;
      align-items: center;

      .text {
        margin-right: 35px;
      }
    }
  }
}
.btn-group {
  margin: 0 26px;
}
.btns {
  margin-right: 26px;
}
.btn {
  width: 60px;
  height: 32px;
  background: #0052d9;
  border-radius: 3px;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
}
.left_food {
  width: 112px;
  height: 32px;
  background: #0052d9;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  span {
    margin-left: 3px;
  }
}
.alarmDataNeoBox {
  position: relative;
  .pingbi {
    position: absolute;
    bottom: 20px;
    right: 60px;
  }
  .unit {
    margin: 0 10px;
  }
  .itemBox {
    padding: 0 10px;
  }
}
.dialogsNav {
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  .select {
    width: 200px;
  }
  .pingbi {
  }
}
.itemandeBox {
  position: relative;
  .setjxz {
    position: absolute;
    right: 50px;
    top: 40px;
  }
  .setjxz_name_checked {
    position: absolute;
    top: -40px;
  }
  .pingbi {
    position: absolute;
    bottom: 20px;
    right: 60px;
  }
}
.newNnit {
  font-size: 24px;
}
.alarmCycle {
  padding: 18px;
  .alarmItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    border-bottom: 1px solid #2b2e3f;
    //边框虚线
    border-bottom-style: dashed;
    margin-bottom: 20px;
    padding-bottom: 10px;
    padding-left: 15px;
    .itemnumbers {
      width: 100px;
      height: 40px;
      margin-bottom: 10px;
    }
    .name {
      width: 140px;
      font-size: 18px;
    }

    .nameOne {
      position: relative;
      left: -110px;
    }
    .nameTow {
      width: 200px;
      font-size: 18px;
      position: relative;
      left: -130px;
      .nameTow_checked {
        position: absolute;
        top: -6px;
        right: -130px;
      }
    }
  }
  .alarmItem_checked {
    margin-left: 15px;
  }
  .itemnumbers_alarmItem {
    margin-left: 10px;
  }
  .alarmItemBox {
    display: flex;
    justify-content: space-around;
    font-size: 16px;
    margin-bottom: 10px;
    .name {
      width: 120px;
    }
  }
}
.unit {
  font-size: 24px;
  margin-left: 30px;
}
</style>
