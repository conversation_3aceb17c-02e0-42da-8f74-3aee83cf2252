import { SetupStoreId } from "@/enum";
import { defineStore } from "pinia";
import { reactive, toRefs } from "vue";
import apiAjax from "@/api/index";
import configure from "@/utils/configure.js";
const transmitterIdList = Object.keys(configure.typeObj).map((key) => {
  return {
    label: configure.typeObj[key],
    value: key,
  };
});

export const useAlarmInfo = defineStore(
  SetupStoreId.AlarmInfo,
  () => {
    // states
    const data = reactive({
      initType: "realTimeAlarmData", //显示历史告警信息还是实时信息
      // 历史告警数据
      historyAlarmData: {
        getListApiUrl: "/api/jnx/dataAlarm/getAlarmInfo",
        getListApiParams: {
          page: 1,
          size: 10,
          totalElements: 0,
          alarmField: "all",
          alarmFrom: "all",
          alarmType: "all",
          startTime: getCurrentTimestamp(7), // 开始时间
          endTime: getCurrentTimestamp(), // 结束时间
          date: getCurrentTimestamps(7),
        },
        // 历史查询条件表单数据
        fromParams: {
          alarmFromList: {
            title: "发波台",
            value: "alarmFrom",
            data: [
              { label: "全部", value: "all", color: "" },
              ...transmitterIdList,
            ],
          },
          alarmLeveleList: {
            title: "类型",
            value: "alarmField",
            data: [
              { label: "全部", value: "all" },
              { label: "场强", value: "FieldStrength" },
              { label: "信噪比", value: "Snr" },
              { label: "包周差", value: "PackageTD" },
              { label: "授时偏差", value: "Tser" },
              { label: "TOC", value: "Toc" },
              { label: "TOA", value: "Toa" },
              { label: "频率偏差", value: "FrequencyTD" },
              { label: "DUT1当前", value: "Dut1dq" },
              { label: "DUT1预报", value: "Dut1yb" },
              { label: "授时电文1型", value: "Dwd1" },
              { label: "授时电文2型", value: "Dwd2" },
            ],
          },
          alarmTypeList: {
            title: "告警等级",
            value: "alarmType",
            data: [
              { label: "全部", value: "all" },
              { label: "一级告警", value: "1" },
              { label: "二级告警", value: "2" },
              { label: "三级告警", value: "3" },
            ],
          },
        },
        tableData: [],
      },
      // 实时告警数据
      realTimeAlarmData: {
        getListApiUrl: "/api/jnx/dataAlarm/getAlarmRealInfo",
        getListApiParams: {
          page: 1,
          size: 1000,
          totalElements: 0,
        },
        fromParams: {},
        tableData: [],
      },
      isLoading: false, // 是否处于数据请求中
      isMaskedState: false, // 是否处于屏蔽状态
    });

    const init = async () => {
      await search("historyAlarmData");
      // await search("realTimeAlarmData");
    };

    const search = async (type) => {
      data.isLoading = true;
      const porps = { ...data[type] };
      if (
        porps.getListApiParams.date &&
        porps.getListApiParams.date.length > 1
      ) {
        // Handle both timestamp (from Naive UI) and Date object (from Element Plus)
        const startTime = typeof porps.getListApiParams.date[0] === 'number'
          ? porps.getListApiParams.date[0]
          : porps.getListApiParams.date[0].getTime();
        const endTime = typeof porps.getListApiParams.date[1] === 'number'
          ? porps.getListApiParams.date[1]
          : porps.getListApiParams.date[1].getTime();

        porps.getListApiParams.startTime = (startTime + "").slice(0, 10);
        porps.getListApiParams.endTime = (endTime + "").slice(0, 10);
      }
      let prostUrl = {};
      Object.keys(porps.getListApiParams).forEach((key) => {
        if (
          key != "date" &&
          key != "totalElements" &&
          porps.getListApiParams[key]
        ) {
          prostUrl[key] = porps.getListApiParams[key];
        }
      });
      let dataList = await apiAjax.post(porps.getListApiUrl, { ...prostUrl });
      if (dataList) {
        data[type].tableData = dataList[0].content;
        data[type].getListApiParams.totalElements = dataList[0].totalElements;
      }

      // 发送获取列表数据请求
      data.isLoading = false;
    };

    const getValueByKey = (list, value, key) => {
      return list?.find((item) => item.value == value)?.[key];
    };

    const onChildTabClick = async (val) => {
      data.initType = val;
      await search(val);
    };

    const handlePageChange = (type, current) => {
      if (current == "size") {
        data[type].getListApiParams.page = 1;
      }
      search(type);
    };
    // 现在只有实时列表有这个方法，历史列表暂时没有用到这个方法
    const setDateList = (setInData, type) => {
      let objType = {
        update: () => {
          data.realTimeAlarmData.tableData.forEach((i) => {
            if (i.id == setInData.id) {
              Object.assign(i, setInData);
            }
          });
        },
        insert: () => {
          // 检查是否已存在
          const index = data.realTimeAlarmData.tableData.findIndex(
            (item) => item.alarmContent === setInData.alarmContent,
          );
          if (index !== -1) {
            // 如果存在，替换该项
            data.realTimeAlarmData.tableData[index] = setInData;
          } else {
            // 如果不存在，插入到数组开头
            data.realTimeAlarmData.tableData.unshift(setInData);
          }
        },
        delete: () => {
          data.realTimeAlarmData.tableData =
            data.realTimeAlarmData.tableData.filter(
              (item) => item.alarmContent != setInData.showDownType,
            );
        },
      };
      objType[type]();
    };
    const initCharData = async (type, dom) => {
      // 默认图表配置
      const chartData = {
        trend: {
          grid: {
            left: "35px",
            right: "15px",
            top: "15px",
            bottom: "25px",
          },
          xAxis: {
            type: "category",
            data: [],
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            bottom: -4,
            itemHeight: 4,
            icon: "rect",
            textStyle: {
              fontWeight: 400,
              fontSize: 12,
              color: "#2B2E3F",
              lineHeight: 20,
              fontStyle: "normal",
            },
          },
          yAxis: {
            type: "value",
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          series: [
            {
              data: [],
              type: "line",
              showSymbol: false,
              symbolSize: 10,
              itemStyle: {
                color: "#1d7bff",
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: "rgba(25, 120, 255, 0.2)" },
                    { offset: 1, color: "rgba(255, 255, 255, 0)" },
                  ],
                },
                origin: "start",
              },
              lineStyle: {
                width: 2,
                type: "solid",
              },
            },
          ],
        },
        pie: {
          tooltip: {
            trigger: "item",
          },
          legend: {
            orient: "vertical",
            top: "40%",
            right: "10px",
            formatter: function (name) {
              const data = chartData.pie.series[0].data.find(
                (item) => item.name === name,
              );
              return `${name} - ${data?.value || 0}次`;
            },
          },
          series: [
            {
              name: "统计",
              type: "pie",
              radius: ["40%", "60%"],
              center: ["40%", "50%"],
              label: {
                show: true,
                position: "outside",
                formatter: function (params) {
                  return params.percent > 0
                    ? `${params.percent.toFixed(2)}%\n${params.name}--${params.value}次`
                    : `${params.name}--${params.value}次`;
                },
                fontSize: 12,
              },
              labelLine: {
                show: true,
                length: 20,
                length2: 40,
              },
              itemStyle: {
                color: function (params) {
                  const colorList = ["#FF0000", "#FFFF00", "#BBFF00"];
                  return colorList[params.dataIndex];
                },
              },
              data: [],
            },
          ],
        },
        category: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          legend: {
            data: ["一级告警", "二级告警", "三级告警"],
            bottom: 0,
          },
          grid: {
            top: "10%",
            left: "3%",
            right: "4%",
            bottom: "20%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: [],
            },
          ],
          yAxis: [
            {
              type: "value",
            },
          ],
          series: [
            {
              name: "一级告警",
              type: "bar",
              stack: "Ad",
              emphasis: {
                focus: "series",
              },
              itemStyle: {
                color: "#FF0000",
              },
              data: [],
            },
            {
              name: "二级告警",
              type: "bar",
              stack: "Ad",
              emphasis: {
                focus: "series",
              },
              itemStyle: {
                color: "#FFA500",
              },
              data: [],
            },
            {
              name: "三级告警",
              type: "bar",
              stack: "Ad",
              emphasis: {
                focus: "series",
              },
              itemStyle: {
                color: "#BBFF00",
              },
              data: [],
            },
          ],
        },
      };

      // 处理饼图数据
      const processPieData = (data) => {
        console.log("data", data);
        if(data.length===0){
          return;
        }
        const pieData = data[0].alarms.map((item) => {
          let name;
          switch (item.alarmType) {
            case "1":
              name = "一级告警";
              break;
            case "2":
              name = "二级告警";
              break;
            case "3":
              name = "三级告警";
              break;
            default:
              name = "未知级别";
          }
          return { value: item.count, name };
        });
        chartData.pie.series[0].data = pieData;
        dom.pie.getIns().setOption(chartData.pie);
      };

      // 处理分类数据
      const processCategoryData = (data) => {
        const dates = data.map(
          (item) => item.day.split("-")[1] + "-" + item.day.split("-")[2],
        );
        // 初始化堆叠图数据
        const seriesData = {
          一级告警: [],
          二级告警: [],
          三级告警: [],
        };

        // 按日期排序
        data.sort((a, b) => new Date(a.day) - new Date(b.day));

        // 初始化每个告警类型的计数为0
        for (let i = 0; i < data.length; i++) {
          seriesData["一级告警"].push(0);
          seriesData["二级告警"].push(0);
          seriesData["三级告警"].push(0);
        }
        // 填充数据
        data.forEach((item, index) => {
          item.alarms.forEach((alarm) => {
            switch (alarm.alarmType) {
              case "1":
                seriesData["一级告警"][index] += alarm.count;
                break;
              case "2":
                seriesData["二级告警"][index] += alarm.count;
                break;
              case "3":
                seriesData["三级告警"][index] += alarm.count;
                break;
            }
          });
        });
        chartData.category.xAxis[0].data = dates;
        chartData.category.series[0].data = seriesData["一级告警"];
        chartData.category.series[1].data = seriesData["二级告警"];
        chartData.category.series[2].data = seriesData["三级告警"];
        // dom.pie.setInfos();
        dom.pie.getIns().setOption(chartData.category);
      };

      // API 请求处理
      const urlApi = {
        historyAlarmData: async () => {
          try {
            //堆叠图
            const pieData = await apiAjax.post(
              "/api/jnx/dataAlarm/getHistoryAlarmTrend",
              {
                alarmFrom: data.historyAlarmData.getListApiParams.alarmFrom,
                alarmField: data.historyAlarmData.getListApiParams.alarmField,
                startTime: data.historyAlarmData.getListApiParams.startTime,
                endTime: data.historyAlarmData.getListApiParams.endTime,
              },
            );
            console.log(pieData);
            processCategoryData(pieData);
          } catch (error) {
            console.error("Failed to fetch history alarm data:", error);
          }
        },
        realTimeAlarmData: async () => {
          try {
            const pieData = await apiAjax.post(
              "/api/jnx/dataAlarm/getAlarmTrend?isDaily=true",
            );
            console.log("pieData", pieData);
            processPieData(pieData);
          } catch (error) {
            console.error("Failed to fetch real-time alarm data:", error);
          }
        },
      };

      await urlApi[type]();
    };

    function getValueLabel(value, options) {
      const labelMap = options.reduce((acc, option) => {
        acc[option.value] = option.label;
        return acc;
      }, {});
      return labelMap[value] || "未知";
    }

    function getCurrentTimestamp(daysAgo = 0) {
      const now = Date.now();
      const daysInMillis = daysAgo * 24 * 60 * 60 * 1000;
      return Math.floor((now - daysInMillis) / 1000);
    }

    function getCurrentTimestamps(daysAgo = 0) {
      const now = new Date();
      const daysInMillis = daysAgo * 24 * 60 * 60 * 1000;
      const pastDate = new Date(now - daysInMillis);
      return [pastDate, now];
    }
    return {
      ...toRefs(data),
      init,
      search,
      getValueByKey,
      onChildTabClick,
      handlePageChange,
      setDateList,
      initCharData,
      getValueLabel,
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  },
);
