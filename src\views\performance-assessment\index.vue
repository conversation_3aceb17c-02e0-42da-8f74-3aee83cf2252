<template>
  <div class="con">
    <div class="top-bar">
      <el-form inline>
        <el-form-item prop="date" class="date-item" label="时间选择">
          <n-date-picker
            v-model:value="form.date"
            type="datetimerange"
            separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item label="报表类型">
          <n-select v-model:value="form.dateType" placeholder="请选择" class="date-type" :options="dateTypeList" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="seach-btn">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="content">
      <div class="left-con">
        <!-- 表头
			选择器 序号 日期  最大值、最小值、准确度、稳定度   -->
        <el-table :data="tableData" border style="width: 100%; margin-bottom: 16px;"
          height="650">
          <el-table-column type="selection" label="选择器" width="62" />
          <el-table-column type="index" :index="indexMethod" label="序号" width="100" />
          <el-table-column property="date" label="时间" />
          <el-table-column property="type" label="类型" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link @click="onDetail(scope.$index, scope.row)">
                立即查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination class="page" :page-size="10" layout="total, prev, pager, next" :total="100" />
      </div>

      <div class="right-con">
        <!-- <img class="file-con" :src="FileImage" /> -->
        <div class="large-btn-con">
          <el-button class="large-btn" :icon="ZoomIn" title="弹窗查看" circle color="#" @click="clickHandle" />
        </div>
        <iframe src="./doc/test.pdf" class="file-con"></iframe>
      </div>

      <el-dialog v-model="dialog.show" :title="dialog.title" width="80%">
        <!-- <img class="file-con" :src="FileImage" /> -->
        <iframe src="./doc/test.pdf" class="file-con" style="height: 700px"></iframe>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
// import FileImage from '@/assets/imgs/baogao.png';
import { ZoomIn } from '@element-plus/icons-vue';
import { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults';

const dateTypeList = ref([
  { label: '天', value: 'd' },
  { label: '月', value: 'm' },
  { label: '季', value: 'q' },
  { label: '年', value: 'y' },
]);

const dialog = reactive({
  show: false,
  title: '文档内容',
})

const form = reactive({
  date: null,
  dateType: 'd'
})

const clickHandle = () => {
  dialog.show = true;
}

const indexMethod = (index: number) => {
  return index + 1;
}

const onDetail = () => {
  dialog.show = true;
}

const tData = [
  { date: '2016-06-01', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-02', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-03', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-04', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-05', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-06', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-07', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-08', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-09', min: 31, max: 67, val1: 45, val2: 38 },
  { date: '2016-06-10', min: 31, max: 67, val1: 45, val2: 38 },
];
tData.forEach((d, i) => d.type = '日报告')
const tableData = ref(tData);

interface Row {
  id: string
  date: string
  type: string
  min: number
  max: number
  val1: number
  val2: number
}

interface SpanMethodProps {
  row: Row
  column: TableColumnCtx<Row>
  rowIndex: number
  columnIndex: number
}

const objectSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  if (columnIndex < 2) {
    if (rowIndex % 5 === 0) {
      return {
        rowspan: 5,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.con {
  .top-bar {
    height: 80px;
    background: #FFF;
    border-radius: 2px;
    padding: 0 24px;
    display: flex;
    align-items: center;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }

    .date-type {
      width: 325px;
    }

    .seach-btn {
      margin-left: 12px;
      margin-right: 4px;
    }
  }

  .content {
    margin-top: 16px;
    display: flex;
  }

  :deep(.el-card__body) {
    display: flex;
    flex-direction: row;
    height: calc(100% - 40px);
  }

  .left-con {
    width: calc(100% - 515px);
    height: 100%;
    margin-right: 16px;

    .page {
      float: right;
    }

    :deep(.el-table) {
      --el-table-border-color: #E7E7E7;

      .cell {
        padding: 0 24px;
      }

      .el-table__cell {
        padding: 16px 0;
        font-weight: 600;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        font-style: normal;
      }

      .el-table__header {
        height: 54px;

        .cell {
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.9);
          line-height: 22px;
          text-align: left;
          font-style: normal;
        }

        th.el-table__cell {
          background-color: #FAFAFA;
        }
      }
    }
  }

  .right-con {
    width: 515px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
  }

  .date-item {
    width: 468px;
  }

  .file-con {
    width: 100%;
    height: 100%;
  }

  .large-btn-con {
    position: relative;
    .large-btn {
      position: absolute;
      width: 32px;
      height: 32px;
      top: 12px;
      left: 50px;
      border: none;
      font-size: 19px;
    }
  }
}
</style>
