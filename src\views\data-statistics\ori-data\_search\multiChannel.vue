<template>
  <div class="oriData_select">
    <div class="oriData_select_flex">
      <div class="oriData_select_item">
        <div>多通道设备ID</div>
        <n-select
          v-model:value="mode.manyChannelDeviceId"
          placeholder="请选择"
          class="select"
          :options="deviceOptions"
        />
      </div>
      <!-- <div class="oriData_select_item">
        <div>通道</div>
        <el-select
          v-model="mode.manyChannelId"
          placeholder="请选择"
          class="select"
        >
          <el-option label="全部" value="all"></el-option>
          <el-option
            :label="item.channelName"
            v-for="item in props.configObj.Configuration"
            :key="item.transmitterId"
            :value="item.transmitterId"
            @click="onSeaechDatareceiver(item)"
          ></el-option>
        </el-select>
        <div class="select-label">
          {{ channeName }}
        </div>
      </div> -->
      <!-- <div class="oriData_select_item">
        <div>类型</div>
        <el-select v-model="mode.dataType" placeholder="请选择" class="select">
          <el-option label="1PPS时差" value="0"></el-option>
          <el-option label="GTP" value="1"></el-option>
        </el-select>
      </div> -->
      <div class="oriData_select_item">
        <div>时间选择</div>
        <div class="select" style="width: 400px">
          <n-date-picker
            v-model:value="mode.placeholderDates"
            type="daterange"
            separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :is-date-disabled="disabledDate"
            @update:value="onChangeDate"
          />
        </div>
      </div>
      <div class="oriData_select_item">
        <div>报表类型</div>
        <n-select
          v-model:value="mode.dateType"
          placeholder="请选择"
          class="select"
          :options="dateTypeOptions"
        />
      </div>
    </div>
    <div class="btnList">
      <div class="btn" @click="onSeaechData">查询</div>
      <div class="btn" @click="emit('onFilePath')">导出</div>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from "vue";
import dayjs from "dayjs";
const props = defineProps({
  configObj: {
    type: Object, //参数类型
    default: () => {
      return {
        Configuration: [],
        Receiver: [],
      };
    },
  },
});
let onblete = {
  businessApp: "2", //业务应用
  manyChannelId: "", //通道ID
  cycleEnd: "", //统计周期结束
  cycleStart: "", //统计周期开始
  // dataType: "", //数据类型（如：场强、包周差、…）
  dateType: "日报", //统计报表类型（比如：1:年、2:月、3:周、4:日）
  manyChannelDeviceId: "all", //接收机ID
  placeholderDates: null,
};
const mode = reactive({
  ...onblete,
});

// Naive UI 选项数据
const deviceOptions = computed(() => [
  { label: "全部", value: "all" },
  ...(props.configObj.Configuration || []).map(item => ({
    label: item.deviceName,
    value: item.deviceId
  }))
]);

const dateTypeOptions = [
  { label: "日报", value: "日报" },
  { label: "周报", value: "周报" },
  { label: "月报", value: "月报" },
  { label: "季报", value: "季报" },
  { label: "年报", value: "年报" }
];
const disabledDate = (time) => {
  const currentDate = new Date();
  return time > currentDate.getTime();
};
const emit = defineEmits(["onSeaechData", "onFilePath"]);
const onSeaechData = () => {
  emit("onSeaechData", mode);
};
const onChangeDate = (val) => {
  if (val && val.length === 2) {
    mode.cycleStart = dayjs(val[0]).format("YYYY-MM-DD HH:mm:ss");
    mode.cycleEnd = dayjs(val[1]).format("YYYY-MM-DD HH:mm:ss");
    // 使用 Date 对象解析原始时间
    let date = new Date(mode.cycleEnd);
    // 设置时间为当天的最后一秒
    date.setHours(23, 59, 59, 0);
    // 格式化日期对象为字符串
    mode.cycleEnd = date.toISOString().replace("T", " ").slice(0, 19);
  }
};
</script>

<style lang="scss" scoped>
.oriData_select {
  background: #ffffff;
  margin: 12px 0;
  display: flex;
  flex-direction: column;
  padding: 24px;
  .oriData_select_flex {
    display: flex;
    flex-wrap: wrap;

    .oriData_select_item {
      display: flex;
      align-items: center;
      margin-left: 26px;

      .select {
        width: 150px;
        margin-left: 8px;
      }

      .select-label {
        margin-left: 6px;
        margin-right: 10px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: center;
        font-style: normal;
      }
    }
  }

  .btnList {
    display: flex;
    margin-left: auto;
    margin-top: 20px;

    .btn {
      width: 60px;
      height: 32px;
      background: #0052d9;
      border-radius: 3px;
      text-align: center;
      line-height: 32px;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      margin-left: 16px;
      cursor: pointer;
    }
  }
}
</style>
