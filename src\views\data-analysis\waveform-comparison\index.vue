<template>
  <!-- 罗兰波形展示组件 - 主容器 -->
  <div class="fuseDome">
    <!-- <div class="btnList">
      <el-button
        v-for="city in cities"
        :key="city.id"
        :class="['panel_setting', { 'is-active': selectedCity === city.id }]"
        @click="handleCitySelect(city)"
        >{{ city.name }}</el-button
      >
    </div> -->

    <!-- 显示五个图形，每个图形包含两条线 -->
    <div class="fuseDome-charts">
      <div
        v-for="(chart, index) in chartItems"
        :key="index"
        class="fuseDome-item"
      >
        <div class="chart-container">
          <div class="name-tag">{{ chart.name }}</div>
          <lineChart
            class="detail-chart"
            :data="chart.chartOption"
            ref="lineChartRef"
          />
          <!-- 数据表格显示相关参数 -->
          <el-table
            :data="chart.tableData"
            class="detail-table"
            :header-cell-style="tableHeaderStyle"
          >
            <el-table-column prop="time" align="center" label="发播台" />
            <el-table-column prop="value" align="center" label="峰值有效功率" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 导入Vue核心功能
import { ref, onMounted, onUnmounted, reactive, getCurrentInstance } from "vue";
// 导入WebSocket相关API
import { initScocket, subscribe, unsubscribe, unsubscribeAll } from "@/api/ws";
// 导入时间格式化工具
// 导入自定义图表组件
import lineChart from "@/components/lineChart/lineChart.vue";
import apiAjax from "@/api/index";

const { proxy } = getCurrentInstance();

// 常量定义
const MAX_DATA = 45; // 最大数据时间范围(分钟)
const MAX_DATA_POINTS = MAX_DATA * 60; // 最大数据点数量(按每秒一个数据点计算)

const selectedCity = ref("J02");

const lineChartRef = ref(null);
/**
 * 创建基础图表配置
 * @param {string} name - 图表名称
 * @returns {Object} 图表配置对象
 */
const createBaseChartOption = (name) => ({
  // 提示框配置
  tooltip: {
    trigger: "axis", // 触发类型：坐标轴触发
    formatter: function (params) {
      // 自定义提示框内容格式
      const date = new Date(params[0].value[0]);
      let result =
        date.getFullYear() +
        "-" +
        (date.getMonth() + 1) +
        "-" +
        date.getDate() +
        " " +
        date.getHours() +
        ":" +
        date.getMinutes() +
        ":" +
        date.getSeconds() +
        "<br/>";

      params.forEach((param) => {
        result += param.seriesName + ": " + param.value[1] + "<br/>";
      });

      return result;
    },
    axisPointer: {
      animation: false, // 禁用指示器动画
    },
    backgroundColor: "rgba(255,255,255,0.9)", // 提示框背景色
    borderColor: "#f0f0f0", // 提示框边框颜色
    textStyle: { color: "#666" }, // 提示框文字样式
  },
  // 图表网格配置
  grid: {
    left: "9%",
    right: "5%",
    bottom: "3%",
    top: "15%",
    containLabel: true, // 包含坐标轴标签
  },
  // 图例配置
  legend: {
    data: ["波形", "幅度"],
    top: 0,
  },
  // X轴配置
  xAxis: {
    type: "time", // 时间类型
    splitLine: { show: true, lineStyle: { color: "#f5f5f5" } }, // 分隔线
    axisLine: { lineStyle: { color: "#333" } }, // 坐标轴线
    axisTick: { show: false }, // 隐藏刻度线
    axisLabel: {
      color: "#333", // 标签颜色
      interval: "auto", // 自动间隔
      hideOverlap: true, // 隐藏重叠标签
    },
  },
  // Y轴配置
  yAxis: {
    type: "value", // 数值类型
    name: name, // Y轴名称
    nameTextStyle: { color: "#333", padding: [10, 30, 0, 0] }, // 名称文字样式
    boundaryGap: [0, "100%"], // 边界间隙
    splitLine: { lineStyle: { color: "#f5f5f5" } }, // 分隔线
    axisLine: { show: false }, // 隐藏坐标轴线
    axisTick: { show: false }, // 隐藏刻度线
    axisLabel: { color: "#333" }, // 标签颜色
  },
  animation: false, // 禁用动画
  // 系列配置 - 现在有两条线
  series: [
    {
      name: "波形", // 第一条线名称
      data: [], // 初始数据为空
      type: "line", // 折线图类型
      symbol: "none", // 不显示数据点标记
      lineStyle: { color: "#5470c6", width: 1 }, // 蓝色线条样式
      itemStyle: { color: "#5470c6" },
    },
    {
      name: "幅度", // 第二条线名称
      data: [], // 初始数据为空
      type: "line", // 折线图类型
      symbol: "none", // 不显示数据点标记
      lineStyle: { color: "#3ba272", width: 1 }, // 黄色线条样式
      itemStyle: { color: "#3ba272" },
    },
  ],
});

// 图表项数据 - 五组罗兰波形
const chartItems = reactive([
  {
    name: "波形 1",
    chartOption: createBaseChartOption("波形 1 (V)"),
    tableData: [{ time: "6000M", value: "--" }], // 初始表格数据
  },
  {
    name: "波形 2",
    chartOption: createBaseChartOption("波形 2 (V)"),
    tableData: [{ time: "6000M", value: "--" }], // 初始表格数据
  },
  {
    name: "波形 3",
    chartOption: createBaseChartOption("波形 3 (V)"),
    tableData: [{ time: "6000M", value: "--" }], // 初始表格数据
  },
  {
    name: "波形 4",
    chartOption: createBaseChartOption("波形 4 (V)"),
    tableData: [{ time: "6000M", value: "--" }], // 初始表格数据
  },
  {
    name: "波形 5",
    chartOption: createBaseChartOption("波形 5 (V)"),
    tableData: [{ time: "6000M", value: "--" }], // 初始表格数据
  },
]);
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
const newGetYMinMax = (newData, oldData) => {
  let absYmax = Math.abs(newData);
  let absYmin = Math.abs(oldData);
  let range = absYmax > absYmin ? absYmax : absYmin;
  // 看这个数 在那个最近的五的倍速中
  let rate = Math.ceil(range / 5) * 5;
  return {
    Ymax: rate,
    Ymin: -rate,
  };
};
function addMinutes(date, minutes) {
  const newDate = new Date(date);
  const newMinutes = newDate.getMinutes() + minutes;
  newDate.setMinutes(newMinutes);

  return formatDate(newDate);
}
/**
 * 格式化时间戳
 * @param {number} timestamp - 秒级时间戳
 * @param {string} [timeZone='UTC'] - 时区 (当前实现固定为UTC, 此参数暂时无效)
 * @param {string} [format] - 输出格式 ('YYYY-MM-DD HH:mm:ss' 或 'HH:mm:ss')
 * @returns {string|null} 格式化后的时间字符串或null
 */
const formatTimestamps = (
  timestamp,
  timeZone = "UTC",
  format = "YYYY-MM-DD HH:mm:ss",
) => {
  if (timestamp === null || timestamp === undefined || isNaN(timestamp))
    return null;
  try {
    const date = new Date(timestamp * 1000); // Convert seconds to milliseconds

    // 总是提取所有部分，因为可能需要它们
    const year = date.getUTCFullYear();
    const month = (date.getUTCMonth() + 1).toString().padStart(2, "0");
    const day = date.getUTCDate().toString().padStart(2, "0");
    const hours = date.getUTCHours().toString().padStart(2, "0");
    const minutes = date.getUTCMinutes().toString().padStart(2, "0");
    const seconds = date.getUTCSeconds().toString().padStart(2, "0");

    // 根据 format 参数决定输出
    if (format === "HH:mm:ss") {
      return `${hours}:${minutes}:${seconds}`;
    }

    // 默认返回完整格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error("Error formatting timestamp:", error);
    return null; // 出错时返回 null
  }
};

/**
 * 更新图表数据的函数
 * @param {Object} data - 接收到的WebSocket数据
 */
const updateData = (newDatas) => {
  const newData = JSON.parse(newDatas);
  const data = newData[selectedCity.value];
  // 检查数据是否为数组
  if (!Array.isArray(data)) {
    console.error("无效数据格式:", data);
    return;
  }

  // 定义通道映射关系 - 根据索引映射到对应图表和通道数据
  const channelMapping = [
    { chartIndex: 0, channel1Key: "channel1", channel2Key: "channel2" },
    { chartIndex: 1, channel1Key: "channel3", channel2Key: "channel4" },
    { chartIndex: 2, channel1Key: "channel5", channel2Key: "channel6" },
    { chartIndex: 3, channel1Key: "channel7", channel2Key: "channel8" },
    { chartIndex: 4, channel1Key: "channel9", channel2Key: "channel10" },
  ];

  // 处理每个数据项
  data.forEach((item, index) => {
    if (!item || typeof item !== "object") return;

    // 获取数据项的站名
    const stationKey = Object.keys(item)[0];
    if (!stationKey) return;

    const stationData = item[stationKey];
    if (!stationData) return;

    // 查找对应的图表索引和通道映射
    const mapping = channelMapping[index];
    if (!mapping) return;

    const chartIndex = mapping.chartIndex;
    const chart = chartItems[chartIndex];
    if (!chart) return;

    // 更新图表名称
    chart.name = stationKey;

    // 更新表格数据
    chart.tableData[0].time = stationKey;

    // 获取时间戳
    const timestampSec = parseInt(stationData.timeSeconds);
    if (isNaN(timestampSec)) {
      console.error("无效的时间戳:", stationData.timeSeconds);
      return;
    }

    const formattedTime = formatTimestamps(timestampSec, "UTC");
    if (!formattedTime) {
      console.error("时间戳格式化失败:", timestampSec);
      return;
    }

    // 获取通道1的值
    const value1 = parseFloat(stationData[mapping.channel1Key]);
    if (isNaN(value1)) {
      console.warn(
        `通道值无效 ${mapping.channel1Key}:`,
        stationData[mapping.channel1Key],
      );
    } else {
      // 更新第一条线(蓝色)的数据
      const seriesData1 = chart.chartOption.series[0].data;
      seriesData1.push([formattedTime, value1]);

      // 如果数据超出X轴最大值，移除最早的数据点
      if (
        chart.chartOption.xAxis.max &&
        new Date(chart.chartOption.xAxis.max).getTime() <
          new Date(formattedTime).getTime()
      ) {
        seriesData1.shift();
      }
    }

    // 获取通道2的值
    const value2 = parseFloat(stationData[mapping.channel2Key]);
    if (isNaN(value2)) {
      console.warn(
        `通道值无效 ${mapping.channel2Key}:`,
        stationData[mapping.channel2Key],
      );
    } else {
      // 更新第二条线(黄色)的数据
      const seriesData2 = chart.chartOption.series[1].data;
      seriesData2.push([formattedTime, value2]);

      // 如果数据超出X轴最大值，移除最早的数据点
      if (
        chart.chartOption.xAxis.max &&
        new Date(chart.chartOption.xAxis.max).getTime() <
          new Date(formattedTime).getTime()
      ) {
        seriesData2.shift();
      }
    }

    // 计算两条线的Y轴范围
    const values = [
      ...chart.chartOption.series[0].data.map((item) => item[1]),
      ...chart.chartOption.series[1].data.map((item) => item[1]),
    ];
    lineChartRef.value.forEach((item) => {
      item.setInfos();
    });
    if (values.length > 0) {
      const maxValue = Math.max(...values);
      const minValue = Math.min(...values);
      const { Ymax, Ymin } = newGetYMinMax(maxValue, minValue);
      chart.chartOption.yAxis.min = Ymin;
      chart.chartOption.yAxis.max = Ymax;

      // 更新X轴范围
      if (chart.chartOption.series[0].data.length > 0) {
        let min, max;

        if (chart.chartOption.xAxis.min && chart.chartOption.xAxis.max) {
          // 如果时间超出了当前范围，更新范围
          if (
            new Date(formattedTime).getTime() >
            new Date(chart.chartOption.xAxis.max).getTime()
          ) {
            min = chart.chartOption.series[0].data[0][0];
            max = formattedTime;
          }
        } else {
          // 首次设置范围
          min = chart.chartOption.series[0].data[0][0];
          const minDate = new Date(min);
          max = addMinutes(minDate, MAX_DATA);
        }

        if (min && max) {
          chart.chartOption.xAxis.min = min;
          chart.chartOption.xAxis.max = max;
        }
      }

      // 更新表格峰值数据
      const peakValue1 = Math.max(
        ...chart.chartOption.series[0].data.map((item) => Math.abs(item[1])),
      );
      const peakValue2 = Math.max(
        ...chart.chartOption.series[1].data.map((item) => Math.abs(item[1])),
      );
      const peakValue = Math.max(peakValue1, peakValue2);
      chart.tableData[0].value = peakValue.toFixed(3) + " kW";
    }
  });
};

// 组件挂载时执行
onMounted(async () => {
  // // 5. 初始化WebSocket连接
  await initScocket();

  // 6. 订阅实时数据
  subscribe(`/topic/jsInfo/modbus/TCP-DTZ-MODBUS`, updateData);

  // await int();
});

const int = async () => {
  try {
    // 1. 获取台站配置信息
    let { data: stationConfig } = await apiAjax.post(
      "/ys/getRealTime?key=DVR-JZJPZ:" + selectedCity.value,
    );

    if (!stationConfig || !stationConfig[0]) {
      console.error("台站配置获取失败");
      return;
    }

    // 2. 解析配置信息
    const stationNames = stationConfig[0];

    // 3. 定义通道与图表的映射关系
    const chartMappings = [
      {
        chartIndex: 0,
        channel1Key: "channel1",
        channel2Key: "channel2",
        nameKeys: ["name1"],
      },
      {
        chartIndex: 1,
        channel1Key: "channel3",
        channel2Key: "channel4",
        nameKeys: ["name2"],
      },
      {
        chartIndex: 2,
        channel1Key: "channel5",
        channel2Key: "channel6",
        nameKeys: ["name3"],
      },
      {
        chartIndex: 3,
        channel1Key: "channel7",
        channel2Key: "channel8",
        nameKeys: ["name4"],
      },
      {
        chartIndex: 4,
        channel1Key: "channel9",
        channel2Key: "channel10",
        nameKeys: ["name5"],
      },
    ];

    // 4. 遍历每个图表，获取对应的数据
    const loadChartDataPromises = [];

    chartMappings.forEach((mapping) => {
      // 查找对应的站名键
      let stationName = "";
      for (const nameKey of mapping.nameKeys) {
        if (stationNames[nameKey]) {
          stationName = stationNames[nameKey];
          break;
        }
      }

      if (!stationName) {
        console.warn(`未找到图表${mapping.chartIndex + 1}对应的站名`);
        return;
      }

      // 更新图表名称
      chartItems[mapping.chartIndex].name = stationName;

      // 更新表格数据
      chartItems[mapping.chartIndex].tableData = [
        { time: stationName, value: "--" },
      ];

      // 构建请求URL
      const url = `/ys/getRealTime?key=DVR:${selectedCity.value}-${mapping.chartIndex + 1}-${stationName}`;

      // 发起请求获取台站数据
      loadChartDataPromises.push(
        apiAjax
          .post(url)
          .then(({ data }) => {
            // 处理台站数据
            if (!data || !Array.isArray(data) || data.length === 0) {
              console.warn(`未获取到图表${mapping.chartIndex + 1}的有效数据`);
              return;
            }

            // 获取数据键名
            const dataKey = Object.keys(data[0])[0];
            if (!dataKey) return;

            const stationData = data[0][dataKey];
            if (!Array.isArray(stationData) || stationData.length === 0) return;

            // 获取当前图表
            const chart = chartItems[mapping.chartIndex];
            const chartOption = chart.chartOption;

            // 清空现有数据
            chartOption.series[0].data = [];
            chartOption.series[1].data = [];

            // 添加数据点
            stationData.forEach((point) => {
              // 处理时间戳
              const timestampSec = parseInt(point.timeSeconds);
              if (isNaN(timestampSec)) return;

              const formattedTime = formatTimestamps(timestampSec, "UTC");
              if (!formattedTime) return;

              // 处理通道1数据
              const value1 = parseFloat(point[mapping.channel1Key]);
              if (!isNaN(value1)) {
                chartOption.series[0].data.push([formattedTime, value1]);
              }

              // 处理通道2数据
              const value2 = parseFloat(point[mapping.channel2Key]);
              if (!isNaN(value2)) {
                chartOption.series[1].data.push([formattedTime, value2]);
              }
            });

            // 排序数据点(按时间)
            chartOption.series[0].data.sort(
              (a, b) => new Date(a[0]) - new Date(b[0]),
            );
            chartOption.series[1].data.sort(
              (a, b) => new Date(a[0]) - new Date(b[0]),
            );

            // 计算Y轴范围
            const allValues = [
              ...chartOption.series[0].data.map((item) => item[1]),
              ...chartOption.series[1].data.map((item) => item[1]),
            ];

            if (allValues.length > 0) {
              const maxValue = Math.max(...allValues);
              const minValue = Math.min(...allValues);

              // 调整Y轴范围
              const { Ymax, Ymin } = newGetYMinMax(maxValue, minValue);
              chartOption.yAxis.min = Ymin;
              chartOption.yAxis.max = Ymax;

              // 设置X轴范围
              if (chartOption.series[0].data.length > 0) {
                const firstPoint = chartOption.series[0].data[0];
                const min = firstPoint[0];
                const minDate = new Date(min);
                const max = addMinutes(minDate, MAX_DATA);
                chartOption.xAxis.min = min;
                chartOption.xAxis.max = max;
              }

              // 更新表格峰值数据
              const peakValue1 = Math.max(
                ...chartOption.series[0].data.map((item) => Math.abs(item[1])),
              );
              const peakValue2 = Math.max(
                ...chartOption.series[1].data.map((item) => Math.abs(item[1])),
              );
              const peakValue = Math.max(peakValue1, peakValue2);
              chart.tableData[0].value = peakValue.toFixed(3) + " kW";
            }
          })
          .catch((err) => {
            console.error(`获取台站${stationName}数据失败:`, err);
          }),
      );
    });

    // 等待所有台站数据加载完成
    await Promise.all(loadChartDataPromises);

    // // 5. 初始化WebSocket连接
    // await initScocket();

    // // 6. 订阅实时数据
    // subscribe(
    //   `/topic/ysInfo/${selectedCity.value}:DVR-REALTIME-DATA`,
    //   updateData,
    // );
  } catch (error) {
    console.error("数据初始化失败:", error);
  }
};

// 组件卸载时执行
onUnmounted(() => {
  unsubscribeAll();
});

// 表格表头样式
const tableHeaderStyle = {
  background: "#f5f7fa",
  color: "#333",
  fontWeight: "bold",
  fontSize: "14px",
  textAlign: "center",
};
</script>

<style lang="scss" scoped>
/* 主容器样式 */
.fuseDome {
  width: 100%;
  // height: 90vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin-top: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05); /* 轻微阴影效果 */
  border-radius: 8px;
  overflow-y: auto; /* 内容溢出时显示滚动条 */
}

/* 图表网格容器 */
.fuseDome-charts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 20px;
  // padding: 15px;
}

/* 每个图表项的容器样式 */
.fuseDome-item {
  width: 100%;
  background: #fafafa;
  border-radius: 6px;
  transition: all 0.3s ease; /* 平滑过渡效果 */

  /* 鼠标悬停时增加阴影效果 */
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  padding: 5px;

  /* 图表名称标签样式 */
  .name-tag {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(24, 144, 255, 0.2);
    backdrop-filter: blur(4px); /* 背景模糊效果 */
    transition: all 0.3s ease;
    z-index: 10;

    /* 名称标签悬停效果 */
    &:hover {
      background: rgba(24, 144, 255, 0.15);
      border-color: rgba(24, 144, 255, 0.3);
    }
  }
}

/* 图表组件样式 */
.detail-chart {
  width: 100%;
  height: 350px;
  background: white;
  border-radius: 4px;
  padding: 5px;
  margin-bottom: 15px;
}

/* 表格组件样式 */
.detail-table {
  background: white;
  border-radius: 4px;
  padding: 10px;

  /* 深度选择器修改Element UI表格头部样式 */
  :deep(.el-table__header) {
    th {
      background-color: #f5f7fa;
    }
  }

  /* 深度选择器修改Element UI表格行悬停效果 */
  :deep(.el-table__row) {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

/* 按钮列表样式 */
.btnList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 12px 15px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.panel_setting {
  font-size: 14px;
  padding: 6px 15px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  background-color: white;
  color: #606266;
}

.panel_setting:hover {
  background-color: #f2f8ff;
  border-color: #a3d0fd;
  color: #1890ff;
}

.panel_setting.is-active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.25);
}
</style>
