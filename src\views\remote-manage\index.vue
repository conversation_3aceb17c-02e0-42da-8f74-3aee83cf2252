<template>
  <div class="con">
    <div class="filter-con">
      <el-form inline :model="form">
        <el-form-item class="date-item" label="时间选择">
          <n-date-picker v-model:value="form.date" type="datetimerange" separator="-"
            start-placeholder="开始时间" end-placeholder="结束时间" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="search-btn">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="content">
      <div class="left-con">
        <el-table :data="tableData" style="width: 100%; margin-bottom: 16px;">
          <el-table-column type="selection" width="55" />
          <el-table-column property="date" label="时间" />
          <el-table-column property="type" label="命令类型" />
          <el-table-column property="source" label="命令来源" />
          <el-table-column property="capacity" label="执行时间" />
          <el-table-column property="state" label="状态">
            <template #default="scope">
              <span :style="scope.row.state === '已执行' ? 'color: #078D5C;' : ''">{{ scope.row.state }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" >
            <template #default="scope">
              <el-button type="primary" link @click="onDo(scope.$index, scope.row)">
                立即处理
              </el-button>
              <el-button type="primary" link @click="onCancel(scope.$index, scope.row)">
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="1000" class="page" />
      </div>
      <div class="right-con">
        <el-card class="top-con card">
          <template #header>
            <div class="card-header">
              <span>命令趋势</span>
            </div>
          </template>

          <LineChart class="chart-area" :data="chartData1" />
        </el-card>

        <el-card class="bottom-con card">
          <template #header>
            <div class="card-header">
              <span>统计命令</span>
            </div>
          </template>

          <LineChart class="chart-area" :data="chartData2" />
          <div class="message">
            <div class="total">命令总数：{{ getTotal() }}</div>
            <div
              v-for="(item, index) in (chartData2.series[0].data || [])"
              :key="item.name"
              class="item"
            >
              <div class="rect" :style="`background: ${chartColors[index]}`" />
              <span class="s1">{{ item.name }}</span>
              <span class="s2">{{ getNumLabel(index) }}条</span>
              <span class="s3">占比</span>
              <span class="s4" :style="`color: ${chartColors[index]};`">{{ getPercentLabel(index) }}%</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import LineChart from '@/components/lineChart/lineChart.vue';
import { chartColors } from '@/constants/chart';
import { useRemoteManage } from '@/store/modules/remote-manage';
import { reactive, ref } from 'vue';

const store = useRemoteManage();

const form = reactive({
  date: null,
});

const tableData = ref([]);
for (let i = 0; i < 10; i++) {
  tableData.value.push({
    date: "2016-05-04",
    type: "重启",
    source: "远程控制平台",
    capacity: "2016-05-05",
    state: "已执行",
  });
}

const getRandomWaveList = () => {
  let list = [];
  for (let i = 0; i < 6; i++) {
    list.push(30 + Math.floor(Math.random() * 350));
  }
  return list;
};

const chartData1 = ref({
  xAxis: {
    type: "category",
    data: ["07-04", "07-05", "07-06", "07-07", "07-08", "07-09"],
  },
  yAxis: {
    type: "value",
    axisLabel: {
      formatter: "{value}", // Change '单位' to your desired unit name
    },
  },
  grid: {
    left: 10,
    right: 10,
    bottom: 20,
    top: 20,
    containLabel: true,
  },
  series: [
    {
      data: getRandomWaveList(),
      type: "line",

      showSymbol: false, // 默认情况下不显示标记
      symbolSize: 10, // 标记的大小
      itemStyle: {
        color: '#1d7bff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(25, 120, 255, 0.2)'  // 0% 处的颜色
          }, {
            offset: 1, color: 'rgba(255, 255, 255, 0)'  // 100% 处的颜色
          }],
        },
        origin: 'start'  // 从起点开始显示阴影
      },
      lineStyle: {
        width: 3, // Set the line width to 4 (or any other desired value)
        type: 'solid'
      },
    },
  ],
  tooltip: {
    trigger: "axis",
  },
});

const chartData2 = ref({
  tooltip: {
    show: false,
  },
  legend: {
    show: false,
    // orient: "vertical",
    // align: "right",
    // top: "middle",
    // right: "30%",
    // icon: "rect",
    // itemWidth: 12,
    // itemHeight: 12,
  },
  grid: {
    left: 0,
    right: 0,
    bottom: 10,
    top: 10,
  },
  series: [
    {
      type: "pie",
      radius: ['45%', '55%'],
      center: ['25%', '50%'],
      data: [
        { value: 12, name: "已处理", itemStyle: { color: chartColors[0] } },
        { value: 34, name: "已执行", itemStyle: { color: chartColors[1] } },
        { value: 44, name: "已取消", itemStyle: { color: chartColors[2] } },
      ],
      label: {
        show: false,
        position: "center"
      },
      emphasis: {
        label: {
          show: true,
            formatter: [
              `{title|{d}%}`,
              `{title|{b} {c}条}`
            ].join('\n'),
            rich: {
              title: {
                fontFamily: 'PingFangSC, PingFang SC, 黑体',
                fontWeight: 400,
                fontSize: 14,
                color: '#2B2E3E',
                lineHeight: 17,
                fontStyle: 'normal',
                align: 'center',
              },
            }
        },
      },
    },
  ],
});

const getNumLabel = (index=0) => {
  const data = chartData2.value.series[0].data;
  return data[index].value;
}

const getPercentLabel = (index=0) => {
  const data = chartData2.value.series[0].data;
  const total = getTotal();
  const itemData = data[index];
  return Number(itemData.value / total * 100).toFixed(2);
}

const getTotal = () => {
  const data = chartData2.value.series[0].data;
  let total = 0;
  data.forEach(item => total += item.value);
  return total;
}

const onDo = () => {

};

const onCancel = () => {

};

const onExport = () => {

};
</script>

<style lang="scss" scoped>
.con {
  .filter-con {
    height: 80px;
    background: #FFF;
    border-radius: 2px;
    padding: 0 24px;
    display: flex;
    align-items: center;

    .item-size {
      width: 180px;
    }

    .item-multi-size {
      width: 440px;
    }

    .date-item {
      width: 467px;
    }

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }

    .date-picker {
      width: 325px;
    }

    .date-type {
      width: 325px;
    }

    .search-btn {
      margin-left: 12px;
      margin-right: 4px;
    }
  }

  .content {
    margin-top: 16px;
    width: 100%;
    height: 870px;
    display: flex;

    :deep(.el-card.is-always-shadow) {
      box-shadow: none;
    }

    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .left-con {
      margin-right: 16px;
      width: calc(100% - 515px);
      height: 100%;
      background: #FFF;
      border-radius: 2px;

      :deep(.el-table) {
        --el-table-border-color: #E7E7E7;

        .cell {
          padding: 0 38px;
        }

        .el-table__cell {
          padding: 16px 0;
          font-weight: 600;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          line-height: 22px;
          font-style: normal;
        }

        .el-table__header {
          height: 54px;

          .cell {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }

          th.el-table__cell {
            background-color: #FAFAFA;
          }
        }
      }

      .page {
        float: right;
        margin-right: 16px;
      }
    }

    .right-con {
      width: 515px;

      .top-con {
        height: 442px;
        background: #FFF;
        border-radius: 2px;

        .chart-area {
          width: 100%;
          height: 350px;
        }
      }

      .bottom-con {
        margin-top: 16px;
        height: 412px;
        background: #FFF;
        border-radius: 2px;

        .chart-area {
          width: 100%;
          height: 310px;
        }

        .message {
          position: relative;
          top: -204px;
          left: 240px;
          width: 240px;

          .total {
            margin-bottom: 12px;
            font-weight: 600;
            font-size: 14px;
            color: #2B2E3F;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }

          .item {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #2B2E3E;
            line-height: 17px;
            text-align: left;
            font-style: normal;

            .rect {
              width: 12px;
              height: 12px;
              margin-right: 3px;
            }

            .s1 {
                margin-right: 44px;
              }

              .s2 {
                margin-right: 17px;
              }

              .s3 {
                margin-right: 17px;
              }

              .s4 {
                margin-right: 4px;
              }
          }
        }
      }
    }
  }
}
</style>
