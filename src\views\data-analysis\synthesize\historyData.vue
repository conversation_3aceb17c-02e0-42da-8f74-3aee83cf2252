<template>
  <div class="synthesize">
    <div class="chaList">
      <div class="navList">
        <div
          class="navItem"
          v-for="key in Object.keys(store.navList)"
          :key="key"
          :class="{ selected: store.navList[key].info }"
          @click="onSwitch(key)"
        >
          <div class="navs">{{ store.navList[key].name }}</div>
          <div class="box_nav">
            <div class="box_value" v-if="key !== 'integrity'">
              {{ store.navList[key].value }}
            </div>
            <lineCharts
              v-if="key == 'continuity'"
              class="chartArea"
              :data="store.navList[key].navChart"
            />
            <div :class="key" v-if="key == 'usability'">
              <!-- <div class="usabilityyuan"></div> -->
              <lineCharts class="chartArea" :data="store.navList[key].navChart" />
            </div>
            <div class="integrity-list" v-if="key == 'integrity'">
              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">总告:</span>
                  <span class="value">{{ store.navList[key].navChart.value.sj_totalAlarms }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">总门:</span>
                  <span class="value">{{ store.navList[key].navChart.value.totalAlarms }}</span>
                </div>
              </div>
              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">授告:</span>
                  <span class="value">{{ store.navList[key].navChart.value.sj_tser }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">授门:</span>
                  <span class="value">{{ store.navList[key].navChart.value.tserAlarms }}</span>
                </div>
              </div>
              <div class="integrity-row">
                <div class="integrity-item">
                  <span class="label">场告:</span>
                  <span class="value">{{ store.navList[key].navChart.value.sj_fieldStrength }}</span>
                </div>
                <div class="integrity-item">
                  <span class="label">场门:</span>
                  <span class="value">{{ store.navList[key].navChart.value.fieldStrengthAlarms }}</span>
                </div>
              </div>
              <div class="integrity-row integrity-row-single">
                <div class="integrity-item">
                  <span class="label">一级告警:</span>
                  <span class="value">{{ store.navList[key].navChart.value.type1Alarms }}</span>
                </div>
              </div>
            </div>
            <div class="progress" v-if="key == 'accuracy'">
              <accurate :accuracy="store.navList[key].value || 0" />
            </div>
            <div class="listCardItem" v-if="key == 'rateBlocking'">
              <img
                class="listCardItem_img"
                src="../../../assets/home/<USER>"
                alt=""
              />
            </div>
            <div class="listCardItem" v-if="key == 'peakEffectivePower'">
              <img
                class="listCardItem_img"
                src="../../../assets/home/<USER>"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
      <div class="chaListBox">
        <lineChart class="chaListBox_item" :data="store.navList[store.historyData.currentSelected].chartData" />
      </div>
    </div>
    <div class="list">
      <el-table :data="store.navList[store.historyData.currentSelected].tableData" class="tableHome" style="width: 100%">
        <el-table-column prop="date" label="时间" />
        <el-table-column prop="name" label="文件名称" />
        <el-table-column prop="name" label="类型" />
        <el-table-column label="周期">
          <template #default="scope">
            <span class="view" @click="handleDelete(scope.$index, scope.row)">
              查看报告
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="paginations"
        background
        layout="prev, pager, next"
        v-model:current-page="store.navList[store.historyData.currentSelected].getListApiParams.page"
        v-model:page-size="store.navList[store.historyData.currentSelected].getListApiParams.size"
        :total="store.navList[store.historyData.currentSelected].getListApiParams.totalElements"
        @size-change="store.handlePageChange('historyData', 'size')"
        @current-change="store.handlePageChange('historyData', 'page')"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import lineCharts from "@/components/lineChart/lineCharts.vue";
import accurate from "@/components/accurate/index.vue";
import { useSynthesizeInfo } from "@/store/modules/data-analysis/synthesize";

const store = useSynthesizeInfo();

const onSwitch = (key) => {
  store.setCurrentSelected(key);
};

const search = () => {
  store.search('historyData');
};

const initData = () => {
  if (store.navList[store.historyData.currentSelected].tableData.length > 0) {
    store.setCurrentSelected('continuity');
  }
};

defineExpose({
  search,
  initData,
  currentSelected: store.historyData.currentSelected,
});
</script>

<style lang="scss" scoped>
.synthesize {
  display: flex;
  flex-direction: column;
  .chaList {
    margin: 16px 0;
    padding: 24px;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .navList {
      display: flex;
      border-bottom: 1px solid #e9e9e9;
      .navItem {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-weight: 600;
        font-size: 20px;
        color: #266fe8;
        margin-left: 16px;
        opacity: 0.7;
        border-bottom: 2px solid #ffffff;
        cursor: pointer;
        .box_nav {
          display: flex;
          align-items: center;
          flex: 1;
        }
        .box_value {
          font-size: 24px;
          color: rgba(0, 0, 0, 0.85);
          margin-left: 15px;
        }
        .usability {
          position: relative;
          .usabilityyuan {
            width: 114px;
            height: 116px;
            border-radius: 50%;
            border: 2px solid #266fe8;
            position: absolute;
            left: 68%;
            transform: translateX(-50%);
          }
        }

        .chartArea {
          width: 120px;
          height: 120px;
          margin-left: 65px;
        }
        .integrity {
          margin-left: 0;
          width: 100%;
          height: 120px;
        }
      }
      .navItem:nth-child(1) {
        margin-left: 0;
      }
      .selected {
        border-bottom: 2px solid #266fe8;
        opacity: 1;
      }
    }
    .chaListBox {
      height: 500px;
      padding-bottom: 15px;
    }
  }
  .list {
    background-color: #fff;
    margin-top: 16px;
    :deep(.el-pagination) {
      margin: 10px;
      justify-content: end;
    }
  }
  .tableHome {
    .view {
      font-size: 14px;
      color: #0052d9;
    }
    :deep(.is-leaf) {
      background: #fafafa;
    }
    :deep(.el-table__cell) {
      padding: 10px 0;
    }
  }
}
.progress {
  height: 120px;
  width: 80%;
  height: auto;
  position: relative;
  margin-left: 10%;
  .progress_nav {
    font-weight: 600;
    font-size: 42px;
    color: rgba(0, 0, 0, 0.9);
    width: 92px;
    height: 22px;
    top: -120px;
    left: -35px;
    background: transparent;
  }
}
.listCardItem {
  .listCardItem_img {
    margin-left: 10px;
    width: 261px;
    height: 120px;
  }
}
.paginations {
  margin: 16px;
  margin-left: auto;
}
.integrity-list {
  width: 100%;
  .integrity-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    &.integrity-row-single {
      justify-content: center;
      .integrity-item {
        width: 100%;
        text-align: center;
      }
    }
    .integrity-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      .label {
        color: #606266;
        margin-right: 5px;
        font-size: 14px;
      }
      .value {
        color: #266fe8;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }
}
</style>
