<template>
  <div class="con">
    <div class="top-bar">
      <el-form inline>
        <el-form-item prop="date" class="date-item" label="时间选择">
          <n-date-picker v-model:value="form.date" type="datetimerange" separator="-"
            start-placeholder="开始时间" end-placeholder="结束时间" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="seach-btn">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card class="card">
      <template #header>
        <div class="card-header">
          <span>实时日志</span>
        </div>

      </template>
      <swiper :slidesPerView="8" :spaceBetween="25" :autoplay="{
        delay: 2500,
        disableOnInteraction: false,
      }" :direction="'vertical'" :modules="modules" class="mySwiper" :loop="false">
        <swiper-slide class="swiperSlide" v-for="item in 40" :key="item">
          <div class="swiperSlideItem">
              操作{{ item }}
          </div>
        </swiper-slide>
      </swiper>
    </el-card>

    <div class="content">
      <div class="left-con">
        <!-- 表头
			  选择器 序号 日期  最大值、最小值、准确度、稳定度   -->
        <el-table :data="tableData" border style="width: 100%; margin-bottom: 16px;">
          <el-table-column type="selection" label="选择器" width="62" />
          <el-table-column type="index" :index="indexMethod" label="序号" width="200" />
          <el-table-column property="date" label="执行时间" />
          <el-table-column property="message" label="文件名称" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link @click="onDetail(scope.$index, scope.row)">
                立即查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination class="page" :page-size="10" layout="total, prev, pager, next" :total="100" />
      </div>

      <el-dialog v-model="dialog.show" :title="dialog.title" width="70%" style="height: 740px">
        <div>
          <el-table border :data="detailData" style="width: 100%; margin-bottom: 16px;" heigth="700">
            <el-table-column type="index" label="序号" width="160" />
            <el-table-column property="date" label="时间" />
            <el-table-column property="data" label="记录" />
            <el-table-column property="name" label="操作人员" />
          </el-table>

          <el-pagination class="page" :page-size="10" layout="total, prev, pager, next" :total="100" />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="js">
import { reactive, ref } from 'vue';
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay, Pagination } from "swiper/modules";

const modules = ref([Autoplay, Pagination]);
const form = reactive({
  date: null,
})

const dialog = reactive({
  show: false,
  title: '日志详情'
});

const tableData = ref([
  { message: '日志记录_2024-06-01', date: '2024-06-01' },
  { message: '日志记录_2024-06-02', date: '2024-06-02' },
  { message: '日志记录_2024-06-03', date: '2024-06-03' },
  { message: '日志记录_2024-06-04', date: '2024-06-04' },
  { message: '日志记录_2024-06-05', date: '2024-06-05' },
  { message: '日志记录_2024-06-06', date: '2024-06-06' },
  { message: '日志记录_2024-06-07', date: '2024-06-07' },
  { message: '日志记录_2024-06-08', date: '2024-06-08' },
  { message: '日志记录_2024-06-09', date: '2024-06-09' },
  { message: '日志记录_2024-06-10', date: '2024-06-10' },
]);

const detailData = ref([
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
  {
    date: "2024-06-01",
    source: "2024****05.log",
    data: "操作记录********",
    name: "张**三",
  },
]);

const indexMethod = (index) => {
  return index + 1;
}

const onDetail = (index, rowData) => {
  dialog.show = true;
}

</script>

<style lang="scss" scoped>
.con {
  .top-bar {
    height: 80px;
    background: #FFF;
    border-radius: 2px;
    padding: 0 24px;
    display: flex;
    align-items: center;

    :deep(.el-form-item) {
      margin-bottom: 0;
    }

    :deep(.el-form--inline .el-form-item) {
      margin-right: 12px;
    }

    .date-type {
      width: 325px;
    }

    .seach-btn {
      margin-left: 12px;
      margin-right: 4px;
    }
  }

  :deep(.el-card.is-always-shadow) {
    box-shadow: none;
  }

  .card {
    height: 300px;
    margin-top: 16px;
    background: #FFF;
    border-radius: 2px;

    :deep(.el-card__body) {
      padding-top: 14px;
      padding-bottom: 14px;
    }

    .card-header {
      font-weight: 600;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .mySwiper {
  overflow: hidden;
  :deep(.swiper-wrapper) {
    height: 230px;
  }
  .swiperSlide {
    // height: 25px !important;
    width: 100%;
    .swiperSlideItem {
      width: 100%;
      display: flex;
      align-content: center;
      justify-content: space-between;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      // margin-bottom: 13px;
    }
  }
}
  }

  .content {
    margin-top: 16px;
    display: flex;
  }

  .left-con {
    width: 100%;
    height: 100%;
  }

  .right-con {
    width: 515px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
  }

  .date-item {
    width: 468px;
  }

  .file-con {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .large-btn-con {
    position: relative;
    top: -682px;
    right: -89%;
    width: 0;
    height: 0;

    .large-btn {
      width: 40px;
      height: 40px;
    }
  }

  .page {
    float: right;
  }

  :deep(.el-table) {
    --el-table-border-color: #E7E7E7;

    .cell {
      padding: 0 24px;
    }

    .el-table__cell {
      padding: 16px 0;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      font-style: normal;
    }

    .el-table__header {
      height: 54px;

      .cell {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      th.el-table__cell {
        background-color: #FAFAFA;
      }
    }
  }
}
</style>
