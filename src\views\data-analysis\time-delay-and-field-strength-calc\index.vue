<template>
  <div class="timeDelayAndFieldStrengthCalc">
    <div id="container"></div>
    <div class="control">
      <div class="PointSelection">
        <n-select
          v-model:value="startValue"
          filterable
          tag
          style="width: 180px"
          placeholder="请选择设备"
          @update:value="onchange(startValue, 'startValue')"
          class="select"
          :options="
            options.map((item) => ({ label: item.label, value: item.value }))
          "
        />
        <div class="start">
          <span class="name">起点</span>
          <el-input v-model="control.start" style="width: 300px" />
        </div>

        <n-select
          v-model:value="endValue"
          filterable
          tag
          style="width: 180px"
          placeholder="请选择设备"
          @update:value="onchange(endValue, 'endValue')"
          class="select"
          :options="
            options.map((item) => ({ label: item.label, value: item.value }))
          "
        />
        <div class="end">
          <span class="name">终点</span>
          <el-input v-model="control.end" style="width: 300px" />
        </div>
        <div class="btn">
          <el-button type="primary" @click="calculation">计算</el-button>
        </div>
      </div>
      <div class="list">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="yuan" label="距离（大圆公式）" />
          <el-table-column prop="vincenty" label="距离（Vincenty）" />
          <el-table-column prop="timeDelay" label="时延（地波）" />
          <el-table-column prop="timeDelay" label="时延（一跳天波）" />
          <el-table-column prop="strength" label="场强（混合路径）" />
          <el-table-column prop="strength" label="场强（单一路径）" />
        </el-table>
      </div>
    </div>
    <div class="selecting">
      <el-button type="primary" @click="getinfo">{{
        mymapSelecting.length == 0 && mymapInfo ? "选点" : "重新选点"
      }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onUnmounted, nextTick } from "vue";
import { calcDistanceBearing } from "./Vincenty";
import { dayuan } from "./dayuan";
import EndImg from "../../../assets/imgs/EndImg.png";
import end from "../../../assets/imgs/end.png";
const startValue = ref("");
const endValue = ref("");
const options = [
  {
    value: "6780M",
    label: "6780M 南海-贺州",
    data: `23°58'3.8640"N，111°43'10.596"E`,
  },
  {
    value: "6780X",
    label: "6780X 南海-饶平",
    data: `23°43'25.932"N，116°53'44.808"E`,
  },
  {
    value: "6780Y",
    label: "6780Y 南海-崇左",
    data: `22°32'35.448"N，107°13'18.0480"E`,
  },
  {
    value: "7430M",
    label: "7430M 北海-荣成",
    data: `37°3'51.7680"N，122°19'25.968"E`,
  },
  {
    value: "7430X",
    label: "7430X 北海-宣城",
    data: `31°4'7.932"N，118°53'9.852"E`,
  },
  {
    value: "7430Y",
    label: "7430Y 北海-和龙",
    data: `42°43'11.568"N，129°6'27.216"E`,
  },
  {
    value: "8390M",
    label: "8390M 东海-宣城",
    data: `31°4'7.932"N，118°53'9.852"E`,
  },
  {
    value: "8390X",
    label: "8390X 东海-饶平",
    data: `23°43'25.932"N，116°53'44.8440"E`,
  },
  {
    value: "8390Y",
    label: "8390Y 东海-荣成",
    data: `37°3'51.7680"N，122°19'25.968"E`,
  },
  {
    value: "6000M",
    label: "6000M 蒲城",
    data: `34°56'55.86"N，109°32'33.6480"E`,
  },
];

const onchange = (val, type) => {
  if (type == "startValue") {
    control.start = options.find((option) => option.value === val).data;
  } else if (type == "endValue") {
    control.end = options.find((option) => option.value === val).data;
  }
};

// 定义响应式引用
const map = ref(null);
// 选点的数组
let mymapSelecting = ref([]);
let mymapInfo = ref(true);
//下面的列表
const tableData = reactive([
  {
    timeDelay: "1s",
    strength: "1100",
    yuan: "0KM",
    vincenty: "0KM",
    algorithm: "x+y",
  },
]);

//下面的输入框
let control = reactive({
  start: "",
  end: "",
});
//点击重新选点
const getinfo = () => {
  map.value.clearOverlays(); // 清空点位
  mymapSelecting.value = [];
  mymapInfo.value = true;
  control.start = "";
  control.end = "";
  startValue.value = "";
  endValue.value = "";
};

function parseDMSString(dmsString) {
  // 去除空格，按逗号分割
  const parts = dmsString.split("，").map((part) => part.trim());
  console.log("parts", parts);
  let latlng = convertDMSToLatLng(parts[0], parts[1]);
  return latlng;
}

//计算点击按钮
const calculation = () => {
  map.value.clearOverlays(); // 清空点位
  try {
    if (control.start && control.end) {
      mymapSelecting.value = [];
      let start = parseDMSString(control.start);
      let controlEnd = parseDMSString(control.end);
      addOverlay(start, EndImg);
      nextTick(() => {
        addOverlay(controlEnd, end);
        calculateDistance();
      });
    }
  } catch (e) {
    console.log(e);
  }
};
// 初始化地图
const initMap = () => {
  // 加载百度地图脚本（假设已经加载）
  let BMap = window.BMap;

  // 创建地图实例
  map.value = new BMap.Map("container");
  const Point = new BMap.Point(109.058002, 34.14262);
  map.value.centerAndZoom(Point, 6);
  map.value.setMinZoom(5);
  map.value.setMaxZoom(10);
  map.value.enableScrollWheelZoom(true);
  // 地图点击事件
  map.value.addEventListener("click", handleMapClick);

  setTimeout(() => {
    document.querySelector(".anchorBL").style.display = "none";
    document.querySelector(".BMap_cpyCtrl").style.display = "none";
  }, 100);
};
//添加点位
const addOverlay = (lnglat, url) => {
  let BMap = window.BMap;
  const Point = new BMap.Point(lnglat[0], lnglat[1]);
  map.value.addOverlay(new BMap.Marker(Point, { enableDragging: false }));
  mymapSelecting.value.push(lnglat);
  nextTick(() => {
    const data = document.querySelectorAll(".BMap_Marker");
    let img, div;
    if (mymapSelecting.value.length == 1) {
      img = data[1].querySelector("img");
      div = data[1].querySelector("div");
    } else {
      img = data[3].querySelector("img");
      div = data[3].querySelector("div");
    }
    img.setAttribute("src", url);
    img.setAttribute("class", "EndImg");
    div.setAttribute("class", "EndImg");
  });
};

//  处理标记拖拽的方法
// const handleMarkerDrag = (e) => {
//   console.log("执行点位拖拽", e);
//   getAddrByPoint(e.point);
// };

// 处理地图点击的方法
const handleMapClick = (e) => {
  let lnglat = [e.point.lng, e.point.lat];
  if (mymapSelecting.value.length == 0) {
    mymapInfo.value ? addOverlay(lnglat, EndImg) : "";
  } else if (mymapSelecting.value.length == 1) {
    mymapInfo.value ? addOverlay(lnglat, end) : "";
    console.log("mymapSelecting.value", mymapSelecting.value);
    const start = convertLatLngToDMS(
      mymapSelecting.value[0][0],
      mymapSelecting.value[0][1],
    );
    const newEnds = convertLatLngToDMS(
      mymapSelecting.value[1][0],
      mymapSelecting.value[1][1],
    );
    // 经度longitude
    // 纬度latitude
    control.start = start.latitude + "，" + start.longitude;
    control.end = newEnds.latitude + "，" + newEnds.longitude;
    calculateDistance();
  } else {
    mymapInfo.value ? (mymapSelecting.value = []) : "";
    mymapInfo.value = false;
  }
  mymapInfo.value ? map.value.panTo(e.point) : "";
  // getAddrByPoint(e.point);

  // calcDistanceBearing({});
};

function calculateDistance() {
  let start = {
    x: mymapSelecting.value[0][0],
    y: mymapSelecting.value[0][1],
  };
  let end = {
    x: mymapSelecting.value[1][0],
    y: mymapSelecting.value[1][1],
  };
  tableData[0].vincenty = calcDistanceBearing(start, end);
  tableData[0].yuan = dayuan(start, end);
  // let a2 = calcDistanceBearing(start, end);
  // console.log("vse1", a2);
  // let yuan2 = dayuan(start, end);
  // console.log("圆1", yuan2);
}

// 根据坐标获取地址的方法
const getAddrByPoint = (Point) => {
  let BMap = window.BMap;
  console.log("执行解析", Point);
  var geoc = new BMap.Geocoder();
  geoc.getLocation(Point, (rs) => {
    console.log("点击地址-获取信息", rs);
  });
  console.log("执行解析完毕");
};

// 在组件挂载时初始化地图
onMounted(() => {
  initMap();
});
onUnmounted(() => {
  map.value = null;
});

// 示例：将经纬度转换为度分秒格式
function convertLatLngToDMS(lng, lat) {
  const decimalToDMS = (decimal) => {
    const isNegative = decimal < 0;
    decimal = Math.abs(decimal);

    const degrees = Math.floor(decimal);
    const minutesDecimal = (decimal - degrees) * 60;
    const minutes = Math.floor(minutesDecimal);
    const seconds = (minutesDecimal - minutes) * 60;

    return {
      degrees: degrees * (isNegative ? -1 : 1),
      minutes: minutes,
      seconds: seconds.toFixed(2),
    };
  };
  const latDMS = decimalToDMS(lat);
  const lngDMS = decimalToDMS(lng);

  return {
    latitude: `${Math.abs(latDMS.degrees)}°${latDMS.minutes}'${
      latDMS.seconds
    }"${latDMS.degrees < 0 ? "S" : "N"}`,
    longitude: `${Math.abs(lngDMS.degrees)}°${lngDMS.minutes}'${
      lngDMS.seconds
    }"${lngDMS.degrees < 0 ? "W" : "E"}`,
  };
}
//时分秒转化成经纬度
function convertDMSToLatLng(lngDMSString, latDMSString) {
  const parseDMSString = (dmsString) => {
    // 匹配度、分、秒和方向的正则表达式
    const regex = /(\d+)°(\d+)'([\d.]+)"([NSWE])/;
    const match = dmsString.match(regex);

    if (!match) {
      throw new Error("无效的 DMS 字符串格式");
    }

    const degrees = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    const seconds = parseFloat(match[3]);
    const direction = match[4];

    return { degrees, minutes, seconds, direction };
  };

  const dmsToDecimal = (degrees, minutes, seconds, direction) => {
    let decimal = degrees + minutes / 60 + seconds / 3600;
    decimal = parseFloat(decimal.toFixed(5));
    // 根据方向调整符号
    if (direction === "S" || direction === "W") {
      decimal = -decimal;
    }

    return decimal;
  };

  const latDMS = parseDMSString(latDMSString);
  const lngDMS = parseDMSString(lngDMSString);

  const latitude = dmsToDecimal(
    latDMS.degrees,
    latDMS.minutes,
    latDMS.seconds,
    latDMS.direction,
  );
  const longitude = dmsToDecimal(
    lngDMS.degrees,
    lngDMS.minutes,
    lngDMS.seconds,
    lngDMS.direction,
  );

  // return {
  //   latitude: latitude,
  //   longitude: longitude,
  // };
  return [latitude, longitude];
}
</script>

<style lang="scss" scoped>
.timeDelayAndFieldStrengthCalc {
  width: 100%;
  display: flex;
  flex-direction: column;
  #container {
    width: 100%;
    height: 800px;
  }
  .control {
    width: 100%;
    margin-top: 10px;
    height: 100px;
    display: flex;
    flex-direction: column;
  }
  .list {
    margin-top: 10px;
  }
}

.selecting {
  position: fixed;
  top: 120px;
  right: 80px;
  z-index: 33;
}
.PointSelection {
  display: flex;
  padding: 0 30px;
  .name {
    margin: 0 20px;
  }
  .btn {
    margin-left: auto;
  }
}
.start {
  margin-right: 20px;
}
</style>
<style>
.EndImg {
  width: 50px !important;
  height: 50px !important;
}
</style>
