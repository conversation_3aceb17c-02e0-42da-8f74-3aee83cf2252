<template>
  <div class="history">
    <div class="historyBox">
      <div class="oneTable">
        <el-table
          class="table"
          :data="store.historyAlarmData.tableData || []"
          border
          :height="820"
          @cell-dblclick="onDoBtnClick"
          :row-class-name="tableRowClassName"
        >
          <el-table-column property="timeSeconds" label="告警开始时刻">
            <template #default="scope">
              {{
                proxy.configure.formatTimestamp(scope.row.timeSeconds * 1000)
              }}
            </template>
          </el-table-column>
          <el-table-column property="timeSecondEnd" label="告警结束时刻">
            <template #default="scope">
              {{
                proxy.configure.formatTimestamp(scope.row.timeSecondEnd * 1000)
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmFrom" label="发波台">
            <template #default="scope">
              {{
                store.getValueLabel(
                  scope.row.alarmFrom,
                  store.historyAlarmData.fromParams.alarmFromList.data
                )
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmField" label="告警类型" width="160">
            <template #default="scope">
              {{
                store.getValueLabel(
                  scope.row.alarmField,
                  store.historyAlarmData.fromParams.alarmLeveleList.data
                )
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmType" label="告警等级">
            <template #default="scope">
              <span :style="{ color: getAlarmLevelColor(scope.row.alarmType) }">
                {{ getAlarmLevelText(scope.row.alarmType) }}
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column property="alarmContent" label="告警内容" /> -->
          <template #empty>
            <div class="table-empty">
              <img :src="emptyIcon" />
              <div class="text">暂无数据</div>
            </div>
          </template>
        </el-table>
        <el-pagination
          background
          class="paginations"
          v-model:current-page="store.historyAlarmData.getListApiParams.page"
          v-model:page-size="store.historyAlarmData.getListApiParams.size"
          :page-sizes="[10, 20, 30, 40]"
          size="default"
          layout="total,sizes, prev, pager, next"
          :total="store.historyAlarmData.getListApiParams.totalElements"
          @size-change="store.handlePageChange('historyAlarmData', 'size')"
          @current-change="store.handlePageChange('historyAlarmData', 'page')"
        />
      </div>
    </div>
    <div class="historyChart">
      <div class="towTable">
        <el-table border :height="455" :data="gridData.listData">
          <el-table-column property="timeSeconds" width="160" label="告警时间">
            <template #default="scope">
              {{
                proxy.configure.formatTimestamp(scope.row.timeSeconds * 1000)
              }}</template
            >
          </el-table-column>
          <el-table-column property="alarmType" width="100" label="告警等级">
            <template #default="scope">
              <span :style="{ color: getAlarmLevelColor(scope.row.alarmType) }">
                {{ getAlarmLevelText(scope.row.alarmType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column property="alarmContent" label="告警描述" width="220">
            <template #default="scope">
              {{
                store.getValueLabel(
                  scope.row.alarmContent.split("-")[0],
                  store.historyAlarmData.fromParams.alarmFromList.data
                ) +
                "-" +
                store.getValueLabel(
                  scope.row.alarmContent.split("-")[1],
                  store.historyAlarmData.fromParams.alarmLeveleList.data
                )
              }}
            </template>
          </el-table-column>
          <el-table-column property="alarmValue" label="异常值" >
            <template #default="scope">
              <div style="white-space: pre-line;">{{ setalarmValue(scope.row.alarmValue) }}</div>
            </template>
          </el-table-column>
          <template #empty>
            <div class="table-empty">
              <img :src="emptyIcon" />
              <div class="text">暂无数据</div>
            </div>
          </template>
        </el-table>
      </div>
      <el-card class="chartBox pie">
        <template #header>
          <div class="card-header">
            <span
              >告警比例<span v-show="dateRangeText"
                >（{{ dateRangeText }}）</span
              ></span
            >
          </div>
        </template>
        <lineChart class="lineChart" ref="pie" />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import lineChart from '@/components/lineChart/lineCharts.vue'
import emptyIcon from "@/assets/imgs/no_data.png";
import { useAlarmInfo } from "@/store/modules/alarm-list";
import {
  onMounted,
  watch,
  reactive,
  ref,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
import apiAjax from "@/api/index";
import { getAlarmLevelText, getAlarmLevelColor } from "@/utils/alarmUtils";
const { proxy } = getCurrentInstance();
const store = useAlarmInfo();
const gridData = ref({
  listData: [],
  alarmContent: "",
});
const trend = ref();
const pie = ref();
const setalarmValue=(value)=>{
  // 判断如果有逗号（中文或英文）就换行
  if (!value) return value;
  const newValue = value.replace(/[，,]/g, "\n");
  return newValue;
}
// 添加一个计算属性来格式化日期范围
const dateRangeText = computed(() => {
  const dateRange = store.historyAlarmData.getListApiParams.date;
  if (dateRange && dateRange.length === 2) {
    const formatDate = (date) => {
      // Handle both timestamp (from Naive UI) and Date object (from Element Plus)
      const dateObj = typeof date === 'number' ? new Date(date) : date;
      return `${dateObj.getFullYear()}年${
        dateObj.getMonth() + 1
      }月${dateObj.getDate()}日`;
    };
    return `${formatDate(dateRange[0])} 至 ${formatDate(dateRange[1])}`;
  }
  return "";
});

onMounted(async () => {
  initData();
});
watch(
  () => store.initType,
  () => {
    gridData.value.listData = [];
    gridData.value.alarmContent = "";
  }
);
//监听表格数据变化
watch(
  () => store.historyAlarmData.tableData,
  async (newVal) => {
    if (newVal && newVal.length > 0) {
      onDoBtnClick(newVal[0]);
      await store.initCharData("historyAlarmData", {
        trend: trend.value,
        pie: pie.value,
      });
    }
    if(newVal.length===0){
      gridData.value.listData = [];
      gridData.value.alarmContent = "";
    }
  }
);
//初始化左边的表格
const initData = () => {
  if (store.historyAlarmData.tableData[0]) {
    onDoBtnClick(store.historyAlarmData.tableData[0]);
  }
};

const tableRowClassName = ({ row, rowIndex }) => {
  if (row.id === gridData.value.id) {
    return "warning-row";
  }
  return "";
};

const onDoBtnClick = (row) => {
  gridData.value.id = row.id;
  gridData.value.listData = [...row.lsAlarms].reverse();
};

defineExpose({
  initData,
});
</script>

<style lang="scss" scoped>
.history {
  margin-top: 18px;
  width: 100%;
  // height: 800px;
  display: flex;
  .historyBox {
    display: flex;
    width: 100%;
    flex: 1.2;
  }
}

.oneTable {
  width: 100%;
  margin-right: 16px;
}
.towTable {
  flex: 1;
}
.table {
  max-height: 650px;
}
.paginations {
  border-left: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
  display: flex;
  width: 100%;
  background: #fff;
  justify-content: end;
  padding: 10px;
}
.historyChart {
  flex: 1;
  display: flex;
  flex-direction: column;
  .chartBox {
    flex: 1;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    .card-header {
      height: 40px;
      padding: 10px 0px 8px 12px;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }
    :deep(.el-card__header) {
      padding: 0 !important;
    }
    :deep(.el-card__body) {
      flex: 1;
    }
    .lineChart {
      flex: 1;
      height: 314px;
    }
  }
  .pie {
    margin-top: 8px;
  }
}
:deep(.warning-row) {
  background-color: #f0f9eb;
}
.table-empty {
  img {
    margin: 0 auto;
  }
}
</style>

