import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import apiAjax from "@/api/index";
import { computed } from 'vue';
import { usabilityChart,continuityChart } from '@/utils/chart.js';

const getChartData = (name) => {
  return {
    xAxis: {
      type: "category",
      data: ["11h", "12h", "13h", "14h", "15h", "16h"],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value} ms",
      },
    },
    grid: {
      left: "1%",
      right: "1%",
      bottom: "1%",
      top: "20%",
      containLabel: true,
    },
    series: [{
      data: [30, 40, 35, 50, 45, 40],
      type: "line",
      showSymbol: false,
      symbolSize: 10,
      itemStyle: { color: '#1d7bff' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(25, 120, 255, 0.2)' },
            { offset: 1, color: 'rgba(255, 255, 255, 0)' }
          ],
        },
        origin: 'start'
      },
      lineStyle: {
        width: 3,
        type: 'solid'
      },
    }],
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let result = params[0].name + '<br/>';
        params.forEach((param) => {
          result += name + ': ' + `<span class='synthesize_tooltip'>${param.value}</span>` + ' %<br/>';
        });
        return result;
      }
    },
  };
};

// 创建指标基础配置
const createIndicator = (name, value, info = false, options = {}, unit = "") => {
  const base = {
    name,
    value: value + unit,
    info,
    chartData: getChartData(name),
    tableName: options.tableName || name.toLowerCase(),
    tableData: [],
    getListApiParams: {
      page: 1,
      size: 10,
      totalElements: 0,
      ...(options.extraParams || {})
    }
  };

  // 根据不同类型添加特定配置
  switch (options.type) {
    case 'list':
      base.navChart = {
        value: options.value
      };
      break;
    case 'gauge':
      base.navChart = continuityChart(value);
      break;
    case 'liquid':
      base.navChart = usabilityChart(value);
      break;
    case 'bar':
      base.navChart = {
        xAxis: {
          type: 'category',
          data: ['总门 总告', '场门 场告', '授门 授告', "一级告警"]
        },
        yAxis: {
          type: 'value',
          show: false,
        },
        grid: {
          left: "1%",
          right: "1%",
          bottom: "1%",
          top: "1%",
          containLabel: true,
        },
        series: [
          {
            data: [120, 200, 150, 80],
            type: 'bar',
            label: {
              show: true,
              position: 'insideTop',
              color: '#fff',
              fontSize: 12,
            },
          },
          {
            data: [220, 180, 210],
            type: 'bar',
            label: {
              show: true,
              position: 'insideTop',
              color: '#fff',
              fontSize: 12,
            },
          }
        ]
      };
      break;
    default:
      base.navChart = continuityChart(value);
  }

  return base;
};

// 创建导航列表
const createNavList = (isHistory = true) => {
  return {
    continuity: createIndicator("连续性", isHistory ? 80 : 0, true, {
      type: 'gauge',
      color: "#266fe8"
    }, "%"),
    usability: createIndicator("可用性", isHistory ? 50 : 0, false, {
      type: 'liquid'
    }, "%"),
    integrity: createIndicator("完好性", isHistory ? 90 : 0, false, {
      type: 'list',
      value: {
        tserAlarms: "--",// 授门
        sj_tser: "--",// 授告
        fieldStrengthAlarms: "--",// 场门
        sj_fieldStrength: "--",// 场告
        totalAlarms: "--",// 总门
        sj_totalAlarms: "--",// 总告
        type1Alarms: "--",// 一级告警
      }
    }, ""),
    accuracy: createIndicator("精确度", isHistory ? 95 : 0, false, {}, ""),
    rateBlocking: createIndicator("阻断率", isHistory ? 3 : 0, false, {}, "‰"),
    // peakEffectivePower: createIndicator("峰值有效功率", isHistory ? 85 : 0, false, {}, "%")
  };
};

export const useSynthesizeInfo = defineStore(
  SetupStoreId.SynthesizeInfo,
  () => {
    // states
    const data = reactive({
      initType: "historyData", // 显示历史数据还是实时数据
      isLoading: false,

      // 历史数据
      historyData: {
        currentSelected: 'continuity', // 当前选中的指标
        navList: createNavList(true),
        getListApiParams: {
          date: (() => {
            // 默认设置为7天前到现在的时间范围
            const now = new Date(); // 当前时间（精确到秒）
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            sevenDaysAgo.setHours(0, 0, 0, 0); // 7天前的00:00:00
            return [sevenDaysAgo.getTime(), now.getTime()];
          })()
        }
      },

      // 实时数据
      realTimeData: {
        currentSelected: 'continuity', // 当前选中的指标
        navList: createNavList(false)
      }
    });

    // 获取当前模式下的navList
    const navList = computed(() => {
      return data[data.initType].navList;
    });

    // 获取当前选中的指标
    const currentSelected = computed(() => {
      return data[data.initType].currentSelected;
    });

    // 设置当前选中的指标
    const setCurrentSelected = (key) => {
      data[data.initType].currentSelected = key;
      // 重置所有info状态
      Object.keys(data[data.initType].navList).forEach(k => {
        data[data.initType].navList[k].info = k === key;
      });
    };

    // 切换历史/实时数据模式
    const onChildTabClick = (type) => {
      data.initType = type;
      // 切换时重置选中状态
      setCurrentSelected('continuity');
    };

    // 搜索数据
    const search = async (type) => {
      const currentNav = data[type].navList[data[type].currentSelected];
      data.isLoading = true;
      try {
        const res = await apiAjax.get(`/api/${type}/${currentNav.tableName}/list`, {
          params: {
            ...currentNav.getListApiParams,
            ...(type === 'historyData' ? { date: data.historyData.getListApiParams.date } : {})
          }
        });
        if (res.code === 200) {
          currentNav.tableData = res.data.content;
          currentNav.getListApiParams.totalElements = res.data.totalElements;
        }
      } finally {
        data.isLoading = false;
      }
    };

    // 处理分页变化
    const handlePageChange = (type, field) => {
      search(type);
    };


    // 初始化
    const init = async () => {
      // await search(data.initType);
    };

    // 转换实时数据为百分比
    const convertToPercentage = (value) => {
      return Math.round(value * 100);
    };

    // 处理实时数据navList数据
    const setRealTimeNavList = (wsData) => {
      if (!wsData) return;

      const { cof, avail, accuracy, blockingRate, peakPowerRate, complete } = wsData;

      // 更新连续性
      if (cof !== undefined) {
        data.realTimeData.navList.continuity.value = `${convertToPercentage(cof)}%`;
        data.realTimeData.navList.continuity.navChart = continuityChart(convertToPercentage(cof));
      }

      // 更新可用性
      if (avail !== undefined) {
        data.realTimeData.navList.usability.value = `${convertToPercentage(avail)}%`;
        data.realTimeData.navList.usability.navChart = usabilityChart(convertToPercentage(avail));
      }

      // 更新精确度
      if (accuracy !== undefined) {
        data.realTimeData.navList.accuracy.value = accuracy;
      }

      // 更新阻断率
      if (blockingRate !== undefined) {
        data.realTimeData.navList.rateBlocking.value = `${blockingRate.toFixed(3) * 1000}‰`;
      }

      // 更新峰值有效功率
      if (peakPowerRate !== undefined) {
        data.realTimeData.navList.peakEffectivePower.value = `${convertToPercentage(peakPowerRate)}%`;
      }
      if (complete !== undefined) {
        data.realTimeData.navList.integrity.navChart.value = {
          ...complete,
        }
      }

    };

    return {
      ...toRefs(data),
      navList,
      currentSelected,
      setCurrentSelected,
      onChildTabClick,
      search,
      handlePageChange,
      init,
      setRealTimeNavList
    };
  }
);
